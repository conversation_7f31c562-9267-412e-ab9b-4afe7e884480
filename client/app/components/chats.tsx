import { useState, useEffect } from "react";
import { Outlet, useNavigate, useLocation } from "react-router";
import { useChats, useCurrentUser, useUnreadCounts } from "@/lib/hooks/useDatabase";
import { useWebSocket } from "@/lib/hooks/useWebSocket";
import { Chat as ChatType } from "@/lib/database";

type ChatTab = "recent" | "private" | "group";

export default function Chats() {
	const [activeTab, setActiveTab] = useState<ChatTab>("recent");
	const [searchQuery, setSearchQuery] = useState("");
	const navigate = useNavigate();
	const location = useLocation();
	const { chats } = useChats();
	const currentUser = useCurrentUser();
	const { unreadCounts } = useUnreadCounts(currentUser?.id || 0);
	const { isConnected } = useWebSocket();

	// 根据当前路由设置活跃标签
	useEffect(() => {
		const path = location.pathname;
		if (path.includes("/chats")) {
			setActiveTab("recent");
		}
	}, [location]);

	// 过滤聊天列表
	const filteredChats = chats.filter(chat => {
		// 搜索过滤
		if (searchQuery) {
			const query = searchQuery.toLowerCase();
			return chat.name?.toLowerCase().includes(query) ||
				   chat.description?.toLowerCase().includes(query);
		}

		// 标签过滤
		switch (activeTab) {
			case "private":
				return chat.type === 1; // 单聊
			case "group":
				return chat.type === 2; // 群聊
			case "recent":
			default:
				return true; // 显示所有
		}
	});

	// 格式化时间显示
	const formatTime = (dateString: string) => {
		const date = new Date(dateString);
		const now = new Date();
		const diff = now.getTime() - date.getTime();
		const days = Math.floor(diff / (1000 * 60 * 60 * 24));

		if (days === 0) {
			return date.toLocaleTimeString('zh-CN', {
				hour: '2-digit',
				minute: '2-digit'
			});
		} else if (days === 1) {
			return "昨天";
		} else if (days < 7) {
			return `${days}天前`;
		} else {
			return date.toLocaleDateString('zh-CN', {
				month: 'short',
				day: 'numeric'
			});
		}
	};

	// 处理聊天项点击
	const handleChatClick = (chat: ChatType) => {
		navigate(`/chats/${chat.id}`);
	};

	// 获取聊天显示名称
	const getChatDisplayName = (chat: ChatType) => {
		if (chat.type === 1) {
			// 单聊：显示对方用户名（这里需要从成员信息中获取）
			return chat.name || "私聊";
		} else {
			// 群聊：显示群名
			return chat.name || "群聊";
		}
	};

	// 获取未读消息数量
	const getUnreadCount = (chatId: number) => {
		return unreadCounts[chatId] || 0;
	};

	return (
		<div className="flex h-full">
			{/* 左侧聊天列表 */}
			<div className="w-80 border-r flex flex-col">
				{/* 头部 */}
				<div className="p-4 border-b">
					<div className="flex items-center justify-between mb-4">
						<h1 className="text-xl font-semibold">消息</h1>
						<div className="flex items-center space-x-2">
							{!isConnected && (
								<div className="w-2 h-2 bg-red-500 rounded-full" title="连接已断开" />
							)}
							<button className="p-2 hover:bg-gray-100 rounded-lg">
								<svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
								</svg>
							</button>
						</div>
					</div>

					{/* 搜索框 */}
					<div className="relative">
						<input
							type="text"
							placeholder="搜索聊天..."
							value={searchQuery}
							onChange={(e) => setSearchQuery(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
						/>
						<svg className="w-5 h-5 absolute left-3 top-2.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
						</svg>
					</div>
				</div>

				{/* 标签切换 */}
				<div className="flex border-b">
					<button
						onClick={() => setActiveTab("recent")}
						className={`flex-1 py-3 px-4 text-sm font-medium ${
							activeTab === "recent"
								? "text-blue-600 border-b-2 border-blue-600"
								: "text-gray-500 hover:text-gray-700"
						}`}
					>
						最近
					</button>
					<button
						onClick={() => setActiveTab("private")}
						className={`flex-1 py-3 px-4 text-sm font-medium ${
							activeTab === "private"
								? "text-blue-600 border-b-2 border-blue-600"
								: "text-gray-500 hover:text-gray-700"
						}`}
					>
						单聊
					</button>
					<button
						onClick={() => setActiveTab("group")}
						className={`flex-1 py-3 px-4 text-sm font-medium ${
							activeTab === "group"
								? "text-blue-600 border-b-2 border-blue-600"
								: "text-gray-500 hover:text-gray-700"
						}`}
					>
						群聊
					</button>
				</div>

				{/* 聊天列表 */}
				<div className="flex-1 overflow-y-auto">
					{filteredChats.length === 0 ? (
						<div className="p-8 text-center text-gray-500">
							{searchQuery ? "没有找到匹配的聊天" : "暂无聊天记录"}
						</div>
					) : (
						filteredChats.map((chat) => {
							const unreadCount = getUnreadCount(chat.id);
							return (
								<div
									key={chat.id}
									onClick={() => handleChatClick(chat)}
									className="p-4 border-b hover:bg-gray-50 cursor-pointer flex items-center"
								>
									{/* 头像 */}
									<div className="w-12 h-12 rounded-full bg-gray-300 mr-3 flex-shrink-0 flex items-center justify-center">
										{chat.avatar ? (
											<img src={chat.avatar} alt="" className="w-full h-full rounded-full object-cover" />
										) : (
											<span className="text-gray-600 font-medium">
												{chat.type === 2 ? "群" : getChatDisplayName(chat).charAt(0)}
											</span>
										)}
									</div>

									{/* 聊天信息 */}
									<div className="flex-1 min-w-0">
										<div className="flex items-center justify-between mb-1">
											<h3 className="font-medium text-gray-900 truncate">
												{getChatDisplayName(chat)}
											</h3>
											<span className="text-xs text-gray-500 flex-shrink-0">
												{formatTime(chat.updated_at)}
											</span>
										</div>
										<div className="flex items-center justify-between">
											<p className="text-sm text-gray-500 truncate">
												{chat.description || "暂无消息"}
											</p>
											{unreadCount > 0 && (
												<span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
													{unreadCount > 99 ? "99+" : unreadCount}
												</span>
											)}
										</div>
									</div>
								</div>
							);
						})
					)}
				</div>
			</div>

			{/* 右侧聊天内容区域 */}
			<div className="flex-1">
				<Outlet />
			</div>
		</div>
	);
}
