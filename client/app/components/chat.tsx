import { useState, useEffect, useRef } from "react";
import { useParams } from "react-router";
import { useChat, useChatMessages, useCurrentUser } from "@/lib/hooks/useDatabase";
import { useWebSocket } from "@/lib/hooks/useWebSocket";
import { Message } from "@/lib/database";

export default function Chat() {
	const { id } = useParams<{ id: string }>();
	const chatId = id ? parseInt(id) : 0;
	const [messageInput, setMessageInput] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const messagesEndRef = useRef<HTMLDivElement>(null);

	const { chat, members } = useChat(chatId);
	const { messages, addMessage } = useChatMessages(chatId);
	const currentUser = useCurrentUser();
	const { sendChatMessage, onMessage, isConnected } = useWebSocket();

	// 监听新消息
	useEffect(() => {
		if (!chatId) return;

		const unsubscribe = onMessage('new_message', (message: Message) => {
			if (message.chat_id === chatId) {
				addMessage(message);
			}
		});

		return unsubscribe;
	}, [chatId, onMessage, addMessage]);

	// 自动滚动到底部
	useEffect(() => {
		messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
	}, [messages]);

	// 发送消息
	const handleSendMessage = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!messageInput.trim() || !chatId || !currentUser || !isConnected) {
			return;
		}

		setIsLoading(true);

		try {
			// 通过 WebSocket 发送消息
			sendChatMessage(chatId, messageInput.trim());
			setMessageInput("");
		} catch (error) {
			console.error('Failed to send message:', error);
		} finally {
			setIsLoading(false);
		}
	};

	// 格式化消息时间
	const formatMessageTime = (dateString: string) => {
		const date = new Date(dateString);
		return date.toLocaleTimeString('zh-CN', {
			hour: '2-digit',
			minute: '2-digit'
		});
	};

	// 获取聊天显示名称
	const getChatDisplayName = () => {
		if (!chat) return "聊天";

		if (chat.type === 1) {
			// 单聊：显示对方用户名
			const otherMember = members.find(m => m.user_id !== currentUser?.id);
			return chat.name || `用户${otherMember?.user_id}`;
		} else {
			// 群聊：显示群名
			return chat.name || "群聊";
		}
	};

	// 检查消息是否是当前用户发送的
	const isOwnMessage = (message: Message) => {
		return message.sender_id === currentUser?.id;
	};

	if (!chatId) {
		return (
			<div className="flex items-center justify-center h-full text-gray-500">
				<div className="text-center">
					<svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
					</svg>
					<p className="text-lg">选择一个聊天开始对话</p>
				</div>
			</div>
		);
	}

	if (!chat) {
		return (
			<div className="flex items-center justify-center h-full text-gray-500">
				<div className="text-center">
					<p className="text-lg">加载中...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="flex flex-col h-full">
			{/* 聊天头部 */}
			<div className="p-4 border-b flex items-center justify-between">
				<div className="flex items-center">
					<div className="w-10 h-10 rounded-full bg-gray-300 mr-3 flex items-center justify-center">
						{chat.avatar ? (
							<img src={chat.avatar} alt="" className="w-full h-full rounded-full object-cover" />
						) : (
							<span className="text-gray-600 font-medium">
								{chat.type === 2 ? "群" : getChatDisplayName().charAt(0)}
							</span>
						)}
					</div>
					<div>
						<h1 className="text-xl font-semibold">{getChatDisplayName()}</h1>
						{chat.type === 2 && (
							<p className="text-sm text-gray-500">{members.length} 名成员</p>
						)}
					</div>
				</div>

				<div className="flex items-center space-x-2">
					{!isConnected && (
						<span className="text-xs text-red-500">连接已断开</span>
					)}
					<button className="p-2 hover:bg-gray-100 rounded-lg">
						<svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
						</svg>
					</button>
				</div>
			</div>

			{/* 消息列表 */}
			<div className="flex-1 overflow-y-auto p-4 space-y-4">
				{messages.length === 0 ? (
					<div className="text-center text-gray-500 py-8">
						<p>还没有消息，开始聊天吧！</p>
					</div>
				) : (
					messages.map((message) => (
						<div
							key={message.id}
							className={`flex ${isOwnMessage(message) ? 'justify-end' : 'justify-start'}`}
						>
							<div className={`flex items-start max-w-xs lg:max-w-md ${isOwnMessage(message) ? 'flex-row-reverse' : ''}`}>
								{/* 头像 */}
								<div className={`w-8 h-8 rounded-full bg-gray-300 flex-shrink-0 flex items-center justify-center ${isOwnMessage(message) ? 'ml-3' : 'mr-3'}`}>
									<span className="text-xs text-gray-600">
										{message.sender_account?.charAt(0) || message.sender_id.toString().charAt(0)}
									</span>
								</div>

								{/* 消息内容 */}
								<div className={`${isOwnMessage(message) ? 'text-right' : 'text-left'}`}>
									{/* 发送者名称（群聊中显示） */}
									{chat.type === 2 && !isOwnMessage(message) && (
										<div className="text-xs text-gray-500 mb-1">
											{message.sender_nickname || message.sender_account || `用户${message.sender_id}`}
										</div>
									)}

									{/* 消息气泡 */}
									<div
										className={`p-3 rounded-lg ${
											isOwnMessage(message)
												? 'bg-blue-500 text-white'
												: 'bg-gray-100 text-gray-900'
										}`}
									>
										{/* 消息内容 */}
										{message.content_type === 1 ? (
											<p className="whitespace-pre-wrap break-words">{message.content}</p>
										) : (
											<div className="text-sm opacity-75">
												[{message.content_type === 2 ? '图片' :
												   message.content_type === 3 ? '文件' :
												   message.content_type === 4 ? '语音' :
												   message.content_type === 5 ? '视频' : '其他'}]
											</div>
										)}

										{/* 消息时间 */}
										<div className={`text-xs mt-1 ${isOwnMessage(message) ? 'text-blue-100' : 'text-gray-500'}`}>
											{formatMessageTime(message.created_at)}
										</div>
									</div>

									{/* 消息状态（仅自己的消息） */}
									{isOwnMessage(message) && (
										<div className="text-xs text-gray-400 mt-1">
											{message.status === 1 ? '已发送' : '已撤回'}
										</div>
									)}
								</div>
							</div>
						</div>
					))
				)}
				<div ref={messagesEndRef} />
			</div>

			{/* 消息输入框 */}
			<div className="p-4 border-t">
				<form onSubmit={handleSendMessage} className="flex space-x-2">
					<input
						type="text"
						value={messageInput}
						onChange={(e) => setMessageInput(e.target.value)}
						placeholder={isConnected ? "输入消息..." : "连接已断开，无法发送消息"}
						disabled={!isConnected || isLoading}
						className="flex-1 border rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
					/>
					<button
						type="submit"
						disabled={!messageInput.trim() || !isConnected || isLoading}
						className="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
					>
						{isLoading ? "发送中..." : "发送"}
					</button>
				</form>
			</div>
		</div>
	);
}
