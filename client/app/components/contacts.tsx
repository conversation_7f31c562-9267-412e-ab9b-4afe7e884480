import { useState, useEffect } from "react";
import { useNavigate } from "react-router";
import { useChats, useCurrentUser } from "@/lib/hooks/useDatabase";
import { User, Chat } from "@/lib/database";
import { auth } from "@/lib/utils";

type ContactTab = "contacts" | "groups";

interface Contact extends User {
	isOnline?: boolean;
	lastSeen?: string;
}

export default function Contacts() {
	const [activeTab, setActiveTab] = useState<ContactTab>("contacts");
	const [searchQuery, setSearchQuery] = useState("");
	const [contacts, setContacts] = useState<Contact[]>([]);
	const [loading, setLoading] = useState(false);
	const navigate = useNavigate();

	const { chats } = useChats();
	const currentUser = useCurrentUser();

	// 获取联系人列表
	useEffect(() => {
		const fetchContacts = async () => {
			if (!currentUser) return;

			setLoading(true);
			try {
				const serverUrl = auth.getServerUrl();
				const token = auth.getToken();

				if (!serverUrl || !token) return;

				// 这里应该调用后端 API 获取联系人列表
				// 暂时使用模拟数据
				const mockContacts: Contact[] = [
					{
						id: 2,
						account: "user2",
						nickname: "张三",
						avatar: "",
						email: "<EMAIL>",
						phone: "",
						status: 1,
						created_at: new Date().toISOString(),
						updated_at: new Date().toISOString(),
						isOnline: true
					},
					{
						id: 3,
						account: "user3",
						nickname: "李四",
						avatar: "",
						email: "<EMAIL>",
						phone: "",
						status: 1,
						created_at: new Date().toISOString(),
						updated_at: new Date().toISOString(),
						isOnline: false,
						lastSeen: "2小时前"
					}
				];

				setContacts(mockContacts);
			} catch (error) {
				console.error('Failed to fetch contacts:', error);
			} finally {
				setLoading(false);
			}
		};

		fetchContacts();
	}, [currentUser]);

	// 过滤联系人
	const filteredContacts = contacts.filter(contact => {
		if (!searchQuery) return true;
		const query = searchQuery.toLowerCase();
		return contact.account.toLowerCase().includes(query) ||
			   contact.nickname?.toLowerCase().includes(query) ||
			   contact.email?.toLowerCase().includes(query);
	});

	// 过滤群组
	const filteredGroups = chats.filter(chat => {
		if (chat.type !== 2) return false; // 只显示群聊
		if (!searchQuery) return true;
		const query = searchQuery.toLowerCase();
		return (chat.name?.toLowerCase().includes(query) || false) ||
			   (chat.description?.toLowerCase().includes(query) || false);
	});

	// 开始聊天
	const handleStartChat = async (contact: Contact) => {
		try {
			// 查找是否已存在与该联系人的单聊
			const existingChat = chats.find(chat =>
				chat.type === 1 &&
				// 这里需要根据实际的数据结构来判断是否是与该联系人的聊天
				chat.name === contact.account
			);

			if (existingChat) {
				// 如果已存在聊天，直接跳转
				navigate(`/chats/${existingChat.id}`);
			} else {
				// 创建新的聊天
				// 这里应该调用后端 API 创建聊天
				console.log('Create new chat with:', contact);
			}
		} catch (error) {
			console.error('Failed to start chat:', error);
		}
	};

	// 进入群聊
	const handleEnterGroup = (group: Chat) => {
		navigate(`/chats/${group.id}`);
	};

	// 获取显示名称
	const getDisplayName = (contact: Contact) => {
		return contact.nickname || contact.account;
	};

	return (
		<div className="flex flex-col h-full">
			{/* 头部 */}
			<div className="p-4 border-b">
				<div className="flex items-center justify-between mb-4">
					<h1 className="text-xl font-semibold">联系人</h1>
					<button className="p-2 hover:bg-gray-100 rounded-lg">
						<svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
						</svg>
					</button>
				</div>

				{/* 搜索框 */}
				<div className="relative">
					<input
						type="text"
						placeholder="搜索联系人..."
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
					<svg className="w-5 h-5 absolute left-3 top-2.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
					</svg>
				</div>
			</div>

			{/* 标签切换 */}
			<div className="flex border-b">
				<button
					onClick={() => setActiveTab("contacts")}
					className={`flex-1 py-3 px-4 text-sm font-medium ${
						activeTab === "contacts"
							? "text-blue-600 border-b-2 border-blue-600"
							: "text-gray-500 hover:text-gray-700"
					}`}
				>
					联系人 ({filteredContacts.length})
				</button>
				<button
					onClick={() => setActiveTab("groups")}
					className={`flex-1 py-3 px-4 text-sm font-medium ${
						activeTab === "groups"
							? "text-blue-600 border-b-2 border-blue-600"
							: "text-gray-500 hover:text-gray-700"
					}`}
				>
					群组 ({filteredGroups.length})
				</button>
			</div>

			{/* 内容区域 */}
			<div className="flex-1 overflow-y-auto">
				{loading ? (
					<div className="p-8 text-center text-gray-500">
						<div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
						<p>加载中...</p>
					</div>
				) : activeTab === "contacts" ? (
					/* 联系人列表 */
					<div>
						{filteredContacts.length === 0 ? (
							<div className="p-8 text-center text-gray-500">
								{searchQuery ? "没有找到匹配的联系人" : "暂无联系人"}
							</div>
						) : (
							filteredContacts.map((contact) => (
								<div
									key={contact.id}
									onClick={() => handleStartChat(contact)}
									className="p-4 border-b hover:bg-gray-50 cursor-pointer flex items-center"
								>
									{/* 头像 */}
									<div className="w-12 h-12 rounded-full bg-gray-300 mr-3 flex-shrink-0 flex items-center justify-center">
										{contact.avatar ? (
											<img src={contact.avatar} alt="" className="w-full h-full rounded-full object-cover" />
										) : (
											<span className="text-gray-600 font-medium text-lg">
												{getDisplayName(contact).charAt(0)}
											</span>
										)}
									</div>

									{/* 联系人信息 */}
									<div className="flex-1 min-w-0">
										<div className="flex items-center justify-between mb-1">
											<h3 className="font-medium text-gray-900 truncate">
												{getDisplayName(contact)}
											</h3>
											{contact.isOnline && (
												<div className="w-2 h-2 bg-green-500 rounded-full"></div>
											)}
										</div>
										<div className="flex items-center justify-between">
											<p className="text-sm text-gray-500 truncate">
												@{contact.account}
											</p>
											<span className="text-xs text-gray-400">
												{contact.isOnline ? "在线" : contact.lastSeen || "离线"}
											</span>
										</div>
										{contact.email && (
											<p className="text-xs text-gray-400 truncate mt-1">
												{contact.email}
											</p>
										)}
									</div>

									{/* 操作按钮 */}
									<div className="ml-3 flex items-center space-x-2">
										<button
											onClick={(e) => {
												e.stopPropagation();
												handleStartChat(contact);
											}}
											className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg"
											title="发送消息"
										>
											<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
											</svg>
										</button>
									</div>
								</div>
							))
						)}
					</div>
				) : (
					/* 群组列表 */
					<div>
						{filteredGroups.length === 0 ? (
							<div className="p-8 text-center text-gray-500">
								{searchQuery ? "没有找到匹配的群组" : "暂无群组"}
							</div>
						) : (
							filteredGroups.map((group) => (
								<div
									key={group.id}
									onClick={() => handleEnterGroup(group)}
									className="p-4 border-b hover:bg-gray-50 cursor-pointer flex items-center"
								>
									{/* 群组头像 */}
									<div className="w-12 h-12 rounded-full bg-blue-100 mr-3 flex-shrink-0 flex items-center justify-center">
										{group.avatar ? (
											<img src={group.avatar} alt="" className="w-full h-full rounded-full object-cover" />
										) : (
											<span className="text-blue-600 font-medium text-lg">群</span>
										)}
									</div>

									{/* 群组信息 */}
									<div className="flex-1 min-w-0">
										<div className="flex items-center justify-between mb-1">
											<h3 className="font-medium text-gray-900 truncate">
												{group.name || "群聊"}
											</h3>
											<span className="text-xs text-gray-400">
												{new Date(group.updated_at).toLocaleDateString()}
											</span>
										</div>
										<p className="text-sm text-gray-500 truncate">
											{group.description || "暂无群组描述"}
										</p>
									</div>

									{/* 进入按钮 */}
									<div className="ml-3">
										<button
											onClick={(e) => {
												e.stopPropagation();
												handleEnterGroup(group);
											}}
											className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg"
											title="进入群聊"
										>
											<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
											</svg>
										</button>
									</div>
								</div>
							))
						)}
					</div>
				)}
			</div>

			{/* 底部操作栏 */}
			<div className="p-4 border-t">
				{activeTab === "contacts" ? (
					<button className="w-full py-2 bg-blue-500 text-white rounded-lg font-medium flex items-center justify-center hover:bg-blue-600 transition-colors">
						<svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
						</svg>
						添加联系人
					</button>
				) : (
					<button className="w-full py-2 bg-green-500 text-white rounded-lg font-medium flex items-center justify-center hover:bg-green-600 transition-colors">
						<svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
						</svg>
						创建群组
					</button>
				)}
			</div>
		</div>
	);
}
