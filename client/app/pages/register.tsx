import { useState, useEffect } from "react";
import { useNavigate, Link } from "react-router";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { auth } from "@/lib/utils";

interface RegisterForm {
	serverUrl: string;
	account: string;
	password: string;
	confirmPassword: string;
	nickname: string;
	email: string;
	phone: string;
}

export default function Register() {
	const [form, setForm] = useState<RegisterForm>({
		serverUrl: "http://localhost:8080",
		account: "",
		password: "",
		confirmPassword: "",
		nickname: "",
		email: "",
		phone: ""
	});
	const [errors, setErrors] = useState<Partial<RegisterForm>>({});
	const [loading, setLoading] = useState(false);
	const navigate = useNavigate();

	// 组件加载时从 cookie 中恢复服务器地址
	useEffect(() => {
		const savedServerUrl = auth.getServerUrl();
		if (savedServerUrl) {
			setForm(prev => ({ ...prev, serverUrl: savedServerUrl }));
		}
	}, []);

	// 表单验证
	const validateForm = (): boolean => {
		const newErrors: Partial<RegisterForm> = {};

		// 服务器地址验证
		if (!form.serverUrl.trim()) {
			newErrors.serverUrl = "请输入服务器地址";
		} else if (!/^https?:\/\/.+/.test(form.serverUrl)) {
			newErrors.serverUrl = "请输入有效的服务器地址";
		}

		// 账号验证
		if (!form.account.trim()) {
			newErrors.account = "请输入账号";
		} else if (form.account.length < 3) {
			newErrors.account = "账号长度至少3个字符";
		} else if (form.account.length > 50) {
			newErrors.account = "账号长度不能超过50个字符";
		}

		// 密码验证
		if (!form.password) {
			newErrors.password = "请输入密码";
		} else if (form.password.length < 6) {
			newErrors.password = "密码长度至少6个字符";
		}

		// 确认密码验证
		if (!form.confirmPassword) {
			newErrors.confirmPassword = "请确认密码";
		} else if (form.password !== form.confirmPassword) {
			newErrors.confirmPassword = "两次输入的密码不一致";
		}

		// 昵称验证（可选）
		if (form.nickname && form.nickname.length > 50) {
			newErrors.nickname = "昵称长度不能超过50个字符";
		}

		// 邮箱验证（可选）
		if (form.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
			newErrors.email = "请输入有效的邮箱地址";
		}

		// 手机号验证（可选）
		if (form.phone && !/^1[3-9]\d{9}$/.test(form.phone)) {
			newErrors.phone = "请输入有效的手机号";
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	// 处理表单输入
	const handleInputChange = (field: keyof RegisterForm, value: string) => {
		setForm(prev => ({ ...prev, [field]: value }));
		// 清除对应字段的错误
		if (errors[field]) {
			setErrors(prev => ({ ...prev, [field]: undefined }));
		}
	};

	// 处理注册提交
	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!validateForm()) {
			return;
		}

		setLoading(true);

		try {
			const apiUrl = `${form.serverUrl}/api/v1/auth/register`;
			console.log('Attempting register at:', apiUrl);

			const requestBody: any = {
				account: form.account,
				password: form.password
			};

			// 添加可选字段
			if (form.nickname.trim()) {
				requestBody.nickname = form.nickname.trim();
			}
			if (form.email.trim()) {
				requestBody.email = form.email.trim();
			}
			if (form.phone.trim()) {
				requestBody.phone = form.phone.trim();
			}

			const response = await fetch(apiUrl, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(requestBody),
			});

			const data = await response.json();

			if (!response.ok) {
				console.error('Register failed:', {
					status: response.status,
					statusText: response.statusText,
					error: data
				});

				// 处理特定错误
				if (data.code === "ACCOUNT_EXISTS") {
					setErrors({ account: "账号已存在" });
				} else if (data.code === "EMAIL_EXISTS") {
					setErrors({ email: "邮箱已被使用" });
				} else if (data.code === "PHONE_EXISTS") {
					setErrors({ phone: "手机号已被使用" });
				} else {
					setErrors({ serverUrl: data.message || `注册失败 (${response.status})` });
				}
				return;
			}

			console.log('Register successful:', data);

			// 注册成功，保存认证信息
			auth.setToken(data.access_token);
			auth.setUser(data.user);
			auth.setServerUrl(form.serverUrl);

			// 跳转到主页
			navigate("/");
		} catch (err) {
			console.error('Register error:', err);
			setErrors({
				serverUrl: err instanceof Error ? err.message : "注册失败，请检查服务器地址和网络连接"
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div
			id="register"
			className="flex flex-col items-center justify-center h-full"
		>
			<h1 className="text-2xl font-bold mb-6">注册账号</h1>

			<form onSubmit={handleSubmit} className="w-full max-w-sm space-y-4">
				{/* 服务器地址 */}
				<div>
					<label htmlFor="serverUrl" className="block text-sm font-medium mb-1">
						服务器地址 *
					</label>
					<Input
						id="serverUrl"
						type="text"
						value={form.serverUrl}
						onChange={(e) => handleInputChange('serverUrl', e.target.value)}
						className={errors.serverUrl ? "border-red-500" : ""}
						required
					/>
					{errors.serverUrl && (
						<div className="text-red-500 text-xs mt-1">{errors.serverUrl}</div>
					)}
				</div>

				{/* 账号 */}
				<div>
					<label htmlFor="account" className="block text-sm font-medium mb-1">
						账号 *
					</label>
					<Input
						id="account"
						type="text"
						value={form.account}
						onChange={(e) => handleInputChange('account', e.target.value)}
						className={errors.account ? "border-red-500" : ""}
						placeholder="3-50个字符"
						required
					/>
					{errors.account && (
						<div className="text-red-500 text-xs mt-1">{errors.account}</div>
					)}
				</div>

				{/* 密码 */}
				<div>
					<label htmlFor="password" className="block text-sm font-medium mb-1">
						密码 *
					</label>
					<Input
						id="password"
						type="password"
						value={form.password}
						onChange={(e) => handleInputChange('password', e.target.value)}
						className={errors.password ? "border-red-500" : ""}
						placeholder="至少6个字符"
						required
					/>
					{errors.password && (
						<div className="text-red-500 text-xs mt-1">{errors.password}</div>
					)}
				</div>

				{/* 确认密码 */}
				<div>
					<label htmlFor="confirmPassword" className="block text-sm font-medium mb-1">
						确认密码 *
					</label>
					<Input
						id="confirmPassword"
						type="password"
						value={form.confirmPassword}
						onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
						className={errors.confirmPassword ? "border-red-500" : ""}
						placeholder="再次输入密码"
						required
					/>
					{errors.confirmPassword && (
						<div className="text-red-500 text-xs mt-1">{errors.confirmPassword}</div>
					)}
				</div>

				{/* 昵称 */}
				<div>
					<label htmlFor="nickname" className="block text-sm font-medium mb-1">
						昵称
					</label>
					<Input
						id="nickname"
						type="text"
						value={form.nickname}
						onChange={(e) => handleInputChange('nickname', e.target.value)}
						className={errors.nickname ? "border-red-500" : ""}
						placeholder="可选，最多50个字符"
					/>
					{errors.nickname && (
						<div className="text-red-500 text-xs mt-1">{errors.nickname}</div>
					)}
				</div>

				{/* 邮箱 */}
				<div>
					<label htmlFor="email" className="block text-sm font-medium mb-1">
						邮箱
					</label>
					<Input
						id="email"
						type="email"
						value={form.email}
						onChange={(e) => handleInputChange('email', e.target.value)}
						className={errors.email ? "border-red-500" : ""}
						placeholder="可选"
					/>
					{errors.email && (
						<div className="text-red-500 text-xs mt-1">{errors.email}</div>
					)}
				</div>

				{/* 手机号 */}
				<div>
					<label htmlFor="phone" className="block text-sm font-medium mb-1">
						手机号
					</label>
					<Input
						id="phone"
						type="tel"
						value={form.phone}
						onChange={(e) => handleInputChange('phone', e.target.value)}
						className={errors.phone ? "border-red-500" : ""}
						placeholder="可选"
					/>
					{errors.phone && (
						<div className="text-red-500 text-xs mt-1">{errors.phone}</div>
					)}
				</div>

				<Button type="submit" disabled={loading} className="w-full">
					{loading ? "注册中..." : "注册"}
				</Button>

				<div className="text-center text-sm text-gray-600">
					已有账号？{" "}
					<Link to="/login" className="text-blue-600 hover:underline">
						立即登录
					</Link>
				</div>
			</form>
		</div>
	);
}
