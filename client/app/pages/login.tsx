import { useState, useEffect } from "react";
import { useNavigate, Link } from "react-router";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { auth } from "@/lib/utils";

export default function Login() {
	const [serverUrl, setServerUrl] = useState("http://localhost:8080");
	const [account, setAccount] = useState("");
	const [password, setPassword] = useState("");
	const [rememberPassword, setRememberPassword] = useState(false);
	const [autoLogin, setAutoLogin] = useState(false);
	const [startupOnBoot, setStartupOnBoot] = useState(false);
	const [error, setError] = useState("");
	const [loading, setLoading] = useState(false);
	const navigate = useNavigate();

	// 组件加载时迁移旧数据并初始化表单
	useEffect(() => {
		// 迁移 localStorage 数据到 cookie
		auth.migrateFromLocalStorage();

		// 从 cookie 中恢复服务器地址
		const savedServerUrl = auth.getServerUrl();
		if (savedServerUrl) {
			setServerUrl(savedServerUrl);
		}

		// 加载记住的凭证
		const rememberedCredentials = auth.getRememberedCredentials();
		if (rememberedCredentials) {
			setServerUrl(rememberedCredentials.serverUrl);
			setAccount(rememberedCredentials.account);
			setPassword(rememberedCredentials.password);
			setRememberPassword(true);
		}

		// 加载自动登录设置
		const autoLoginEnabled = auth.getAutoLogin();
		setAutoLogin(autoLoginEnabled);

		// 加载开机自启动设置
		const startupOnBootEnabled = auth.getStartupOnBoot();
		setStartupOnBoot(startupOnBootEnabled);

		// 同步系统开机自启动状态
		auth.getSystemStartupStatus().then(systemStatus => {
			if (systemStatus !== startupOnBootEnabled) {
				// 如果系统状态与保存的设置不一致，以系统状态为准
				setStartupOnBoot(systemStatus);
				auth.setStartupOnBoot(systemStatus);
			}
		}).catch(error => {
			console.error('Failed to get system startup status:', error);
		});

		// 如果启用了自动登录且有记住的凭证，自动执行登录
		if (autoLoginEnabled && rememberedCredentials) {
			handleAutoLogin(rememberedCredentials);
		}
	}, []);

	// 自动登录处理
	const handleAutoLogin = async (credentials: { serverUrl: string; account: string; password: string }) => {
		setLoading(true);
		try {
			const apiUrl = `${credentials.serverUrl}/api/v1/auth/login`;
			const response = await fetch(apiUrl, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					account: credentials.account,
					password: credentials.password,
				}),
			});

			const data = await response.json();

			if (response.ok) {
				// 自动登录成功
				auth.setToken(data.access_token);
				auth.setUser(data.user);
				auth.setServerUrl(credentials.serverUrl);
				navigate("/");
			} else {
				// 自动登录失败，清除记住的凭证
				auth.removeRememberedCredentials();
				auth.removeAutoLogin();
				setRememberPassword(false);
				setAutoLogin(false);
				setPassword("");
				console.log('Auto login failed:', data.message);
			}
		} catch (err) {
			console.error('Auto login error:', err);
			// 自动登录失败，清除记住的凭证
			auth.removeRememberedCredentials();
			auth.removeAutoLogin();
			setRememberPassword(false);
			setAutoLogin(false);
			setPassword("");
		} finally {
			setLoading(false);
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setLoading(true);
		setError("");

		try {
			const apiUrl = `${serverUrl}/api/v1/auth/login`;
			console.log('Attempting login at:', apiUrl);
			const response = await fetch(apiUrl, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					account,
					password,
				}),
			});

			const data = await response.json();

			if (!response.ok) {
				console.error('Login failed:', {
					status: response.status,
					statusText: response.statusText,
					error: data
				});
				throw new Error(data.message || `登录失败 (${response.status})`);
			}
			console.log('Login successful:', data);

			// 使用 cookie 保存认证信息
			auth.setToken(data.access_token);
			auth.setUser(data.user);
			auth.setServerUrl(serverUrl);

			// 处理记住密码
			if (rememberPassword) {
				auth.setRememberedCredentials(serverUrl, account, password);
			} else {
				auth.removeRememberedCredentials();
			}

			// 处理自动登录设置
			auth.setAutoLogin(autoLogin);

			// 处理开机自启动设置
			if (startupOnBoot !== auth.getStartupOnBoot()) {
				const success = await auth.applyStartupOnBootSetting(startupOnBoot);
				if (!success) {
					console.warn('Failed to apply startup on boot setting');
				}
			}

			// 跳转到主页
			navigate("/");
		} catch (err) {
			console.error('Login error:', err);
			setError(err instanceof Error ? err.message : "登录失败，请检查服务器地址和网络连接");
		} finally {
			setLoading(false);
		}
	};

	return (
		<div
			id="login"
			className="flex flex-col items-center justify-center h-full"
		>
			<h1 className="text-2xl font-bold mb-6">登录</h1>

			<form onSubmit={handleSubmit} className="w-full max-w-sm space-y-4">
				<div>
					<label htmlFor="serverUrl" className="block text-sm font-medium mb-1">
						服务器地址
					</label>
					<Input
						id="serverUrl"
						type="text"
						value={serverUrl}
						onChange={(e) => setServerUrl(e.target.value)}
						required
					/>
				</div>

				<div>
					<label htmlFor="account" className="block text-sm font-medium mb-1">
						账号
					</label>
					<Input
						id="account"
						type="text"
						value={account}
						onChange={(e) => setAccount(e.target.value)}
						required
					/>
				</div>

				<div>
					<label htmlFor="password" className="block text-sm font-medium mb-1">
						密码
					</label>
					<Input
						id="password"
						type="password"
						value={password}
						onChange={(e) => setPassword(e.target.value)}
						required
					/>
				</div>

				{error && <div className="text-red-500 text-sm">{error}</div>}

				{/* 记住密码和自动登录选项 */}
				<div className="space-y-2">
					<label className="flex items-center">
						<input
							type="checkbox"
							checked={rememberPassword}
							onChange={(e) => setRememberPassword(e.target.checked)}
							className="mr-2"
						/>
						<span className="text-sm text-gray-600">记住密码</span>
					</label>

					<label className="flex items-center">
						<input
							type="checkbox"
							checked={autoLogin}
							onChange={(e) => setAutoLogin(e.target.checked)}
							className="mr-2"
						/>
						<span className="text-sm text-gray-600">自动登录</span>
					</label>

					<label className="flex items-center">
						<input
							type="checkbox"
							checked={startupOnBoot}
							onChange={(e) => setStartupOnBoot(e.target.checked)}
							className="mr-2"
						/>
						<span className="text-sm text-gray-600">开机自启动</span>
					</label>
				</div>

				<Button type="submit" disabled={loading} className="w-full">
					{loading ? "登录中..." : "登录"}
				</Button>

				<div className="text-center text-sm text-gray-600">
					没有账号？{" "}
					<Link to="/register" className="text-blue-600 hover:underline">
						立即注册
					</Link>
				</div>
			</form>
		</div>
	);
}
