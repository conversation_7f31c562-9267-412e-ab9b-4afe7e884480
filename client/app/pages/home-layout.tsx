import { useEffect, useState } from "react";
import { Outlet, useNavigate } from "react-router";
import { auth } from "@/lib/utils";
import { useConnectionStatus } from "@/lib/hooks/useWebSocket";
import { dbManager } from "@/lib/database";

export default function HomeLayout() {
	const navigate = useNavigate();
	const [isValidating, setIsValidating] = useState(true);
	const { isConnected, reconnectAttempts } = useConnectionStatus();

	useEffect(() => {
		const validateAuth = async () => {
			try {
				// 迁移旧的 localStorage 数据
				auth.migrateFromLocalStorage();

				// 检查是否已登录
				if (!auth.isAuthenticated()) {
					navigate("/login");
					return;
				}

				// 验证 token 有效性
				const isValid = await auth.validateToken();
				if (!isValid) {
					navigate("/login");
					return;
				}

				// 初始化数据库
				try {
					await dbManager.openDatabase();
					console.log('Database initialized successfully');
				} catch (dbError) {
					console.error('Failed to initialize database:', dbError);
					// 数据库初始化失败不阻止应用启动，但记录错误
				}

				setIsValidating(false);
			} catch (error) {
				console.error('Auth validation failed:', error);
				navigate("/login");
			}
		};

		validateAuth();
	}, [navigate]);

	// 显示加载状态
	if (isValidating) {
		return (
			<div className="h-screen w-screen flex items-center justify-center">
				<div className="text-lg">验证登录状态...</div>
			</div>
		);
	}
	return (
		<div id="home-layout" className="h-screen w-screen flex flex-col">
			{/* WebSocket 连接状态栏 */}
			{!isConnected && (
				<div className="bg-yellow-100 border-b border-yellow-200 px-4 py-2 text-sm text-yellow-800">
					{reconnectAttempts > 0
						? `正在重连... (尝试 ${reconnectAttempts}/5)`
						: "连接已断开，正在尝试重连..."
					}
				</div>
			)}

			<div className="flex-1 flex">
				<Outlet />
			</div>
		</div>
	);
}
