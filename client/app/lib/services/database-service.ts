import { getCurrentDatabase, User, Chat, ChatMember, Message, LocalSettings } from '../database';

// 数据访问服务类，提供与后端 API 一致的数据操作接口

export class DatabaseService {
	// 用户相关操作
	static async getUser(id: number): Promise<User | undefined> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		return await db.users.get(id);
	}

	static async getUserByAccount(account: string): Promise<User | undefined> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		return await db.users.where('account').equals(account).first();
	}

	static async saveUser(user: User): Promise<void> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		await db.users.put(user);
	}

	static async saveUsers(users: User[]): Promise<void> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		await db.users.bulkPut(users);
	}

	// 会话相关操作
	static async getChat(id: number): Promise<Chat | undefined> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		return await db.chats.get(id);
	}

	static async getAllChats(): Promise<Chat[]> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		return await db.chats.where('status').equals(1).toArray();
	}

	static async saveChat(chat: Chat): Promise<void> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		await db.chats.put(chat);
	}

	static async saveChats(chats: Chat[]): Promise<void> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		await db.chats.bulkPut(chats);
	}

	// 会话成员相关操作
	static async getChatMembers(chatId: number): Promise<ChatMember[]> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		return await db.chat_members.where('chat_id').equals(chatId).toArray();
	}

	static async getUserChats(userId: number): Promise<ChatMember[]> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		return await db.chat_members.where('user_id').equals(userId).toArray();
	}

	static async saveChatMember(member: ChatMember): Promise<void> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		await db.chat_members.put(member);
	}

	static async saveChatMembers(members: ChatMember[]): Promise<void> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		await db.chat_members.bulkPut(members);
	}

	// 消息相关操作
	static async getMessage(id: number): Promise<Message | undefined> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		return await db.messages.get(id);
	}

	static async getChatMessages(chatId: number, limit: number = 50, offset: number = 0): Promise<Message[]> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		return await db.messages
			.where('chat_id')
			.equals(chatId)
			.reverse()
			.offset(offset)
			.limit(limit)
			.toArray();
	}

	static async getLatestMessages(chatId: number, fromSeq: number, limit: number = 50): Promise<Message[]> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		return await db.messages
			.where('chat_id')
			.equals(chatId)
			.and(msg => msg.seq > fromSeq)
			.limit(limit)
			.toArray();
	}

	static async saveMessage(message: Message): Promise<void> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		await db.messages.put(message);
	}

	static async saveMessages(messages: Message[]): Promise<void> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		await db.messages.bulkPut(messages);
	}

	static async updateMessageStatus(messageId: number, status: number): Promise<void> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		await db.messages.update(messageId, { status });
	}

	// 本地设置相关操作
	static async getSetting(key: string): Promise<string | undefined> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		const setting = await db.local_settings.get(key);
		return setting?.value;
	}

	static async setSetting(key: string, value: string): Promise<void> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		await db.local_settings.put({
			key,
			value,
			updated_at: new Date().toISOString()
		});
	}

	static async getAllSettings(): Promise<LocalSettings[]> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		return await db.local_settings.toArray();
	}

	// 数据同步相关操作
	static async getLastSyncTime(): Promise<string | undefined> {
		return await this.getSetting('last_sync_time');
	}

	static async setLastSyncTime(time: string): Promise<void> {
		await this.setSetting('last_sync_time', time);
	}

	static async getLastMessageSeq(chatId: number): Promise<number> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');

		const lastMessage = await db.messages
			.where('chat_id')
			.equals(chatId)
			.reverse()
			.first();

		return lastMessage?.seq || 0;
	}

	// 清理操作
	static async clearChatMessages(chatId: number): Promise<void> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');
		await db.messages.where('chat_id').equals(chatId).delete();
	}

	static async clearAllData(): Promise<void> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');

		await Promise.all([
			db.users.clear(),
			db.chats.clear(),
			db.chat_members.clear(),
			db.messages.clear()
		]);
	}

	// 统计信息
	static async getStorageStats(): Promise<{
		users: number;
		chats: number;
		members: number;
		messages: number;
		totalMessages: number;
	}> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');

		const [usersCount, chatsCount, membersCount, messagesCount] = await Promise.all([
			db.users.count(),
			db.chats.count(),
			db.chat_members.count(),
			db.messages.count()
		]);

		return {
			users: usersCount,
			chats: chatsCount,
			members: membersCount,
			messages: messagesCount,
			totalMessages: messagesCount
		};
	}

	// 搜索功能
	static async searchMessages(chatId: number, keyword: string, limit: number = 20): Promise<Message[]> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');

		return await db.messages
			.where('chat_id')
			.equals(chatId)
			.and(msg => msg.content?.toLowerCase().includes(keyword.toLowerCase()) || false)
			.limit(limit)
			.toArray();
	}

	static async searchChats(keyword: string): Promise<Chat[]> {
		const db = getCurrentDatabase();
		if (!db) throw new Error('Database not initialized');

		return await db.chats
			.filter(chat =>
				(chat.name?.toLowerCase().includes(keyword.toLowerCase()) || false) ||
				(chat.description?.toLowerCase().includes(keyword.toLowerCase()) || false)
			)
			.toArray();
	}
}
