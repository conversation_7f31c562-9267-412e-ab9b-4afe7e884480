import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import Cookies from "js-cookie";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

// 身份验证相关工具函数
export const auth = {
	// Cookie 配置
	cookieOptions: {
		expires: 7, // 7天过期
		secure: process.env.NODE_ENV === 'production', // 生产环境使用 HTTPS
		sameSite: 'strict' as const,
	},

	// 设置认证 token
	setToken(token: string) {
		Cookies.set('auth_token', token, this.cookieOptions);
	},

	// 获取认证 token
	getToken(): string | undefined {
		return Cookies.get('auth_token');
	},

	// 移除认证 token
	removeToken() {
		Cookies.remove('auth_token');
	},

	// 设置用户信息
	setUser(user: any) {
		Cookies.set('user_info', JSON.stringify(user), this.cookieOptions);
	},

	// 获取用户信息
	getUser(): any | null {
		const userStr = Cookies.get('user_info');
		if (!userStr) return null;
		try {
			return JSON.parse(userStr);
		} catch {
			return null;
		}
	},

	// 移除用户信息
	removeUser() {
		Cookies.remove('user_info');
	},

	// 设置服务器地址
	setServerUrl(url: string) {
		Cookies.set('server_url', url, this.cookieOptions);
	},

	// 获取服务器地址
	getServerUrl(): string | undefined {
		return Cookies.get('server_url');
	},

	// 移除服务器地址
	removeServerUrl() {
		Cookies.remove('server_url');
	},

	// 设置记住的登录凭证
	setRememberedCredentials(serverUrl: string, account: string, password: string) {
		const credentials = {
			serverUrl,
			account,
			password: btoa(password), // 简单的 base64 编码（注意：这不是安全的加密）
			timestamp: Date.now()
		};
		Cookies.set('remembered_credentials', JSON.stringify(credentials), {
			...this.cookieOptions,
			expires: 30 // 记住密码保存30天
		});
	},

	// 获取记住的登录凭证
	getRememberedCredentials(): { serverUrl: string; account: string; password: string } | null {
		const credentialsStr = Cookies.get('remembered_credentials');
		if (!credentialsStr) return null;

		try {
			const credentials = JSON.parse(credentialsStr);
			// 检查是否过期（30天）
			const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
			if (credentials.timestamp < thirtyDaysAgo) {
				this.removeRememberedCredentials();
				return null;
			}

			return {
				serverUrl: credentials.serverUrl,
				account: credentials.account,
				password: atob(credentials.password) // 解码
			};
		} catch {
			return null;
		}
	},

	// 移除记住的登录凭证
	removeRememberedCredentials() {
		Cookies.remove('remembered_credentials');
	},

	// 设置自动登录选项
	setAutoLogin(enabled: boolean) {
		Cookies.set('auto_login', enabled.toString(), this.cookieOptions);
	},

	// 获取自动登录选项
	getAutoLogin(): boolean {
		const autoLogin = Cookies.get('auto_login');
		return autoLogin === 'true';
	},

	// 移除自动登录选项
	removeAutoLogin() {
		Cookies.remove('auto_login');
	},

	// 设置开机自启动选项
	setStartupOnBoot(enabled: boolean) {
		Cookies.set('startup_on_boot', enabled.toString(), this.cookieOptions);
	},

	// 获取开机自启动选项
	getStartupOnBoot(): boolean {
		const startupOnBoot = Cookies.get('startup_on_boot');
		return startupOnBoot === 'true';
	},

	// 移除开机自启动选项
	removeStartupOnBoot() {
		Cookies.remove('startup_on_boot');
	},

	// 应用开机自启动设置到系统
	async applyStartupOnBootSetting(enabled: boolean): Promise<boolean> {
		try {
			if (window.electronAPI) {
				const success = await window.electronAPI.setAutoLaunch(enabled);
				if (success) {
					this.setStartupOnBoot(enabled);
				}
				return success;
			}
			return false;
		} catch (error) {
			console.error('Failed to apply startup on boot setting:', error);
			return false;
		}
	},

	// 获取系统开机自启动状态
	async getSystemStartupStatus(): Promise<boolean> {
		try {
			if (window.electronAPI) {
				return await window.electronAPI.getAutoLaunchStatus();
			}
			return false;
		} catch (error) {
			console.error('Failed to get system startup status:', error);
			return false;
		}
	},

	// 清除所有认证信息
	clearAll() {
		this.removeToken();
		this.removeUser();
		this.removeServerUrl();
		this.removeRememberedCredentials();
		this.removeAutoLogin();
		this.removeStartupOnBoot();

		// 同时清理旧的 localStorage 数据（向后兼容）
		localStorage.removeItem('token');
		localStorage.removeItem('user');
		localStorage.removeItem('serverUrl');
	},

	// 检查是否已登录
	isAuthenticated(): boolean {
		return !!this.getToken();
	},

	// 从 localStorage 迁移到 cookie（向后兼容）
	migrateFromLocalStorage() {
		const token = localStorage.getItem('token');
		const user = localStorage.getItem('user');
		const serverUrl = localStorage.getItem('serverUrl');

		if (token) {
			this.setToken(token);
			localStorage.removeItem('token');
		}

		if (user) {
			try {
				this.setUser(JSON.parse(user));
				localStorage.removeItem('user');
			} catch {
				// 忽略解析错误
			}
		}

		if (serverUrl) {
			this.setServerUrl(serverUrl);
			localStorage.removeItem('serverUrl');
		}
	},

	// 验证 token 有效性
	async validateToken(): Promise<boolean> {
		const token = this.getToken();
		const serverUrl = this.getServerUrl();

		if (!token || !serverUrl) {
			return false;
		}

		try {
			const response = await fetch(`${serverUrl}/api/v1/auth/me`, {
				method: 'GET',
				headers: {
					'Authorization': `Bearer ${token}`,
					'Content-Type': 'application/json',
				},
			});

			if (response.ok) {
				const data = await response.json();
				// 更新用户信息
				this.setUser(data.user || data);
				return true;
			} else {
				// Token 无效，清除认证信息
				this.clearAll();
				return false;
			}
		} catch (error) {
			console.error('Token validation failed:', error);
			// 网络错误时不清除 token，允许离线使用
			return false;
		}
	}
};
