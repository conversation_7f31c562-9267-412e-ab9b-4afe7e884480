import Dexie, { Table } from 'dexie';
import { auth } from './utils';

// 数据库接口定义，与后端 MySQL 数据库保持一致

export interface User {
	id: number;
	account: string;
	nickname?: string;
	avatar?: string;
	email?: string;
	phone?: string;
	status: number;
	created_at: string;
	updated_at: string;
}

export interface Chat {
	id: number;
	type: number; // 1: 单聊, 2: 群聊
	name?: string;
	avatar?: string;
	description?: string;
	owner_id?: number;
	last_seq: number;
	last_message_id?: number;
	status: number;
	created_at: string;
	updated_at: string;
}

export interface ChatMember {
	id: number;
	chat_id: number;
	user_id: number;
	role: number; // 1:群主,2:管理员,3:普通成员
	last_read_seq: number;
	joined_at: string;
	muted: boolean;
	nickname_in_chat?: string;
}

export interface Message {
	id: number;
	chat_id: number;
	seq: number;
	sender_id: number;
	type: number;
	content?: string;
	content_type: number;
	status: number;
	reply_to_msg_id?: number;
	created_at: string;
	updated_at: string;
	extra?: string;
	// 扩展字段（本地使用）
	sender_account?: string;
	sender_nickname?: string;
	sender_avatar?: string;
}

// 本地设置表
export interface LocalSettings {
	key: string;
	value: string;
	updated_at: string;
}

// 数据库类
export class LiyuDatabase extends Dexie {
	users!: Table<User>;
	chats!: Table<Chat>;
	chat_members!: Table<ChatMember>;
	messages!: Table<Message>;
	local_settings!: Table<LocalSettings>;

	constructor(databaseName: string) {
		super(databaseName);

		this.version(1).stores({
			users: 'id, account, nickname, email, phone, status, created_at, updated_at',
			chats: 'id, type, name, owner_id, last_seq, last_message_id, status, created_at, updated_at',
			chat_members: 'id, chat_id, user_id, role, last_read_seq, joined_at, muted',
			messages: 'id, chat_id, seq, sender_id, type, content_type, status, reply_to_msg_id, created_at, updated_at',
			local_settings: 'key, value, updated_at'
		});
	}
}

// 数据库管理器
export class DatabaseManager {
	private static instance: DatabaseManager | null = null;
	private database: LiyuDatabase | null = null;
	private currentDatabaseName: string | null = null;

	private constructor() {}

	static getInstance(): DatabaseManager {
		if (!DatabaseManager.instance) {
			DatabaseManager.instance = new DatabaseManager();
		}
		return DatabaseManager.instance;
	}

	// 生成数据库名称
	private generateDatabaseName(serverUrl: string, account: string): string {
		// 清理服务器 URL，移除协议和端口
		const cleanUrl = serverUrl
			.replace(/^https?:\/\//, '')
			.replace(/:\d+$/, '')
			.replace(/[^a-zA-Z0-9]/g, '_');
		
		// 清理账户名
		const cleanAccount = account.replace(/[^a-zA-Z0-9]/g, '_');
		
		return `liyu_${cleanUrl}_${cleanAccount}`;
	}

	// 获取当前数据库名称
	getCurrentDatabaseName(): string | null {
		return this.currentDatabaseName;
	}

	// 打开数据库
	async openDatabase(): Promise<LiyuDatabase> {
		const serverUrl = auth.getServerUrl();
		const user = auth.getUser();

		if (!serverUrl || !user?.account) {
			throw new Error('Missing server URL or user account');
		}

		const databaseName = this.generateDatabaseName(serverUrl, user.account);

		// 如果已经打开了相同的数据库，直接返回
		if (this.database && this.currentDatabaseName === databaseName) {
			return this.database;
		}

		// 关闭当前数据库
		if (this.database) {
			this.database.close();
		}

		// 创建新数据库实例
		this.database = new LiyuDatabase(databaseName);
		this.currentDatabaseName = databaseName;

		try {
			// 打开数据库
			await this.database.open();
			console.log(`Database opened: ${databaseName}`);
			
			// 初始化数据库
			await this.initializeDatabase();
			
			return this.database;
		} catch (error) {
			console.error('Failed to open database:', error);
			throw error;
		}
	}

	// 初始化数据库
	private async initializeDatabase() {
		if (!this.database) return;

		// 检查是否是首次创建
		const settingsCount = await this.database.local_settings.count();
		if (settingsCount === 0) {
			// 首次创建，添加初始设置
			await this.database.local_settings.add({
				key: 'database_version',
				value: '1.0.0',
				updated_at: new Date().toISOString()
			});

			await this.database.local_settings.add({
				key: 'created_at',
				value: new Date().toISOString(),
				updated_at: new Date().toISOString()
			});

			console.log('Database initialized with default settings');
		}
	}

	// 获取当前数据库实例
	getDatabase(): LiyuDatabase | null {
		return this.database;
	}

	// 关闭数据库
	async closeDatabase() {
		if (this.database) {
			this.database.close();
			this.database = null;
			this.currentDatabaseName = null;
			console.log('Database closed');
		}
	}

	// 清除数据库
	async clearDatabase() {
		if (this.database) {
			await this.database.delete();
			this.database = null;
			this.currentDatabaseName = null;
			console.log('Database cleared');
		}
	}

	// 获取数据库统计信息
	async getDatabaseStats() {
		if (!this.database) {
			return null;
		}

		try {
			const [usersCount, chatsCount, membersCount, messagesCount] = await Promise.all([
				this.database.users.count(),
				this.database.chats.count(),
				this.database.chat_members.count(),
				this.database.messages.count()
			]);

			return {
				databaseName: this.currentDatabaseName,
				users: usersCount,
				chats: chatsCount,
				members: membersCount,
				messages: messagesCount
			};
		} catch (error) {
			console.error('Failed to get database stats:', error);
			return null;
		}
	}

	// 检查数据库是否已打开
	isDatabaseOpen(): boolean {
		return this.database !== null && this.database.isOpen();
	}
}

// 导出单例实例
export const dbManager = DatabaseManager.getInstance();

// 便捷函数
export async function getDatabase(): Promise<LiyuDatabase> {
	return await dbManager.openDatabase();
}

export function getCurrentDatabase(): LiyuDatabase | null {
	return dbManager.getDatabase();
}
