import { useEffect, useState, useCallback } from 'react';
import { getWebSocketManager, WebSocketManager, ChatMessage } from '../websocket';

export function useWebSocket() {
	const [isConnected, setIsConnected] = useState(false);
	const [wsManager] = useState<WebSocketManager>(() => getWebSocketManager());

	useEffect(() => {
		// 监听连接状态变化
		const unsubscribe = wsManager.onConnectionState(setIsConnected);
		
		return unsubscribe;
	}, [wsManager]);

	const sendMessage = useCallback((type: string, data: any) => {
		wsManager.send({ type, data });
	}, [wsManager]);

	const sendChatMessage = useCallback((chatId: number, content: string, contentType: number = 1) => {
		wsManager.sendChatMessage(chatId, content, contentType);
	}, [wsManager]);

	const joinChat = useCallback((chatId: number) => {
		wsManager.joinChat(chatId);
	}, [wsManager]);

	const leaveChat = useCallback((chatId: number) => {
		wsManager.leaveChat(chatId);
	}, [wsManager]);

	const onMessage = useCallback((type: string, handler: (data: any) => void) => {
		return wsManager.onMessage(type, handler);
	}, [wsManager]);

	const reconnect = useCallback(() => {
		wsManager.reconnect();
	}, [wsManager]);

	return {
		isConnected,
		sendMessage,
		sendChatMessage,
		joinChat,
		leaveChat,
		onMessage,
		reconnect,
		wsManager
	};
}

export function useChatMessages(chatId: number) {
	const [messages, setMessages] = useState<ChatMessage[]>([]);
	const { onMessage, joinChat, leaveChat } = useWebSocket();

	useEffect(() => {
		if (chatId) {
			// 加入聊天室
			joinChat(chatId);

			// 监听新消息
			const unsubscribeMessage = onMessage('new_message', (message: ChatMessage) => {
				if (message.chat_id === chatId) {
					setMessages(prev => [...prev, message]);
				}
			});

			// 监听消息更新
			const unsubscribeUpdate = onMessage('message_updated', (message: ChatMessage) => {
				if (message.chat_id === chatId) {
					setMessages(prev => prev.map(msg => 
						msg.id === message.id ? message : msg
					));
				}
			});

			// 监听消息删除
			const unsubscribeDelete = onMessage('message_deleted', (data: { message_id: number, chat_id: number }) => {
				if (data.chat_id === chatId) {
					setMessages(prev => prev.filter(msg => msg.id !== data.message_id));
				}
			});

			return () => {
				leaveChat(chatId);
				unsubscribeMessage();
				unsubscribeUpdate();
				unsubscribeDelete();
			};
		}
	}, [chatId, onMessage, joinChat, leaveChat]);

	const addMessage = useCallback((message: ChatMessage) => {
		setMessages(prev => [...prev, message]);
	}, []);

	const updateMessage = useCallback((messageId: number, updates: Partial<ChatMessage>) => {
		setMessages(prev => prev.map(msg => 
			msg.id === messageId ? { ...msg, ...updates } : msg
		));
	}, []);

	const removeMessage = useCallback((messageId: number) => {
		setMessages(prev => prev.filter(msg => msg.id !== messageId));
	}, []);

	return {
		messages,
		addMessage,
		updateMessage,
		removeMessage,
		setMessages
	};
}

export function useConnectionStatus() {
	const [isConnected, setIsConnected] = useState(false);
	const [reconnectAttempts, setReconnectAttempts] = useState(0);
	const { wsManager } = useWebSocket();

	useEffect(() => {
		const unsubscribe = wsManager.onConnectionState((connected) => {
			setIsConnected(connected);
			if (connected) {
				setReconnectAttempts(0);
			}
		});

		// 监听重连尝试
		const unsubscribeReconnect = wsManager.onMessage('reconnect_attempt', (data: { attempt: number }) => {
			setReconnectAttempts(data.attempt);
		});

		return () => {
			unsubscribe();
			unsubscribeReconnect();
		};
	}, [wsManager]);

	return {
		isConnected,
		reconnectAttempts
	};
}
