import { useLiveQuery } from 'dexie-react-hooks';
import { useCallback, useMemo } from 'react';
import { getCurrentDatabase, User, Chat, ChatMember, Message } from '../database';
import { DatabaseService } from '../services/database-service';

// 用户相关 hooks
export function useUser(userId: number) {
	const user = useLiveQuery(
		async () => {
			const db = getCurrentDatabase();
			if (!db || !userId) return null;
			return await db.users.get(userId);
		},
		[userId]
	);

	return user;
}

export function useCurrentUser() {
	const user = useLiveQuery(
		async () => {
			const db = getCurrentDatabase();
			if (!db) return null;
			// 假设当前用户信息存储在本地设置中
			const currentUserId = await DatabaseService.getSetting('current_user_id');
			if (!currentUserId) return null;
			return await db.users.get(parseInt(currentUserId));
		},
		[]
	);

	return user;
}

// 会话相关 hooks
export function useChats() {
	const chats = useLiveQuery(
		async () => {
			const db = getCurrentDatabase();
			if (!db) return [];
			return await db.chats
				.where('status')
				.equals(1)
				.reverse()
				.sortBy('updated_at');
		},
		[]
	);

	const addChat = useCallback(async (chat: Chat) => {
		await DatabaseService.saveChat(chat);
	}, []);

	const updateChat = useCallback(async (chatId: number, updates: Partial<Chat>) => {
		const db = getCurrentDatabase();
		if (!db) return;
		await db.chats.update(chatId, updates);
	}, []);

	return {
		chats: chats || [],
		addChat,
		updateChat
	};
}

export function useChat(chatId: number) {
	const chat = useLiveQuery(
		async () => {
			const db = getCurrentDatabase();
			if (!db || !chatId) return null;
			return await db.chats.get(chatId);
		},
		[chatId]
	);

	const members = useLiveQuery(
		async () => {
			const db = getCurrentDatabase();
			if (!db || !chatId) return [];
			return await db.chat_members.where('chat_id').equals(chatId).toArray();
		},
		[chatId]
	);

	return {
		chat,
		members: members || []
	};
}

// 消息相关 hooks
export function useChatMessages(chatId: number, limit: number = 50) {
	const messages = useLiveQuery(
		async () => {
			const db = getCurrentDatabase();
			if (!db || !chatId) return [];
			return await db.messages
				.where('chat_id')
				.equals(chatId)
				.reverse()
				.limit(limit)
				.toArray();
		},
		[chatId, limit]
	);

	const addMessage = useCallback(async (message: Message) => {
		await DatabaseService.saveMessage(message);
	}, []);

	const updateMessage = useCallback(async (messageId: number, updates: Partial<Message>) => {
		const db = getCurrentDatabase();
		if (!db) return;
		await db.messages.update(messageId, updates);
	}, []);

	const deleteMessage = useCallback(async (messageId: number) => {
		const db = getCurrentDatabase();
		if (!db) return;
		await db.messages.delete(messageId);
	}, []);

	return {
		messages: messages || [],
		addMessage,
		updateMessage,
		deleteMessage
	};
}

// 会话成员相关 hooks
export function useChatMembers(chatId: number) {
	const members = useLiveQuery(
		async () => {
			const db = getCurrentDatabase();
			if (!db || !chatId) return [];
			return await db.chat_members.where('chat_id').equals(chatId).toArray();
		},
		[chatId]
	);

	const addMember = useCallback(async (member: ChatMember) => {
		await DatabaseService.saveChatMember(member);
	}, []);

	const updateMember = useCallback(async (memberId: number, updates: Partial<ChatMember>) => {
		const db = getCurrentDatabase();
		if (!db) return;
		await db.chat_members.update(memberId, updates);
	}, []);

	const removeMember = useCallback(async (memberId: number) => {
		const db = getCurrentDatabase();
		if (!db) return;
		await db.chat_members.delete(memberId);
	}, []);

	return {
		members: members || [],
		addMember,
		updateMember,
		removeMember
	};
}

// 用户会话列表 hooks
export function useUserChats(userId: number) {
	const userChats = useLiveQuery(
		async () => {
			const db = getCurrentDatabase();
			if (!db || !userId) return [];
			
			// 获取用户参与的所有会话
			const chatMembers = await db.chat_members.where('user_id').equals(userId).toArray();
			const chatIds = chatMembers.map(member => member.chat_id);
			
			if (chatIds.length === 0) return [];
			
			// 获取会话详情
			const chats = await db.chats.where('id').anyOf(chatIds).toArray();
			
			// 合并会话和成员信息
			return chats.map(chat => {
				const member = chatMembers.find(m => m.chat_id === chat.id);
				return {
					...chat,
					memberInfo: member
				};
			}).sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
		},
		[userId]
	);

	return userChats || [];
}

// 搜索相关 hooks
export function useSearchMessages(chatId: number, keyword: string) {
	const searchResults = useLiveQuery(
		async () => {
			if (!keyword.trim() || !chatId) return [];
			const db = getCurrentDatabase();
			if (!db) return [];
			
			return await db.messages
				.where('chat_id')
				.equals(chatId)
				.and(msg => msg.content?.toLowerCase().includes(keyword.toLowerCase()) || false)
				.limit(20)
				.toArray();
		},
		[chatId, keyword]
	);

	return searchResults || [];
}

export function useSearchChats(keyword: string) {
	const searchResults = useLiveQuery(
		async () => {
			if (!keyword.trim()) return [];
			const db = getCurrentDatabase();
			if (!db) return [];
			
			return await db.chats
				.filter(chat => 
					chat.name?.toLowerCase().includes(keyword.toLowerCase()) ||
					chat.description?.toLowerCase().includes(keyword.toLowerCase())
				)
				.toArray();
		},
		[keyword]
	);

	return searchResults || [];
}

// 统计信息 hooks
export function useDatabaseStats() {
	const stats = useLiveQuery(
		async () => {
			const db = getCurrentDatabase();
			if (!db) return null;
			
			const [usersCount, chatsCount, membersCount, messagesCount] = await Promise.all([
				db.users.count(),
				db.chats.count(),
				db.chat_members.count(),
				db.messages.count()
			]);

			return {
				users: usersCount,
				chats: chatsCount,
				members: membersCount,
				messages: messagesCount
			};
		},
		[]
	);

	return stats;
}

// 未读消息统计 hooks
export function useUnreadCounts(userId: number) {
	const unreadCounts = useLiveQuery(
		async () => {
			const db = getCurrentDatabase();
			if (!db || !userId) return {};
			
			// 获取用户参与的所有会话
			const chatMembers = await db.chat_members.where('user_id').equals(userId).toArray();
			const counts: Record<number, number> = {};
			
			for (const member of chatMembers) {
				// 计算未读消息数量
				const unreadCount = await db.messages
					.where('chat_id')
					.equals(member.chat_id)
					.and(msg => msg.seq > member.last_read_seq)
					.count();
				
				counts[member.chat_id] = unreadCount;
			}
			
			return counts;
		},
		[userId]
	);

	const markAsRead = useCallback(async (chatId: number, lastReadSeq: number) => {
		const db = getCurrentDatabase();
		if (!db || !userId) return;
		
		await db.chat_members
			.where({ chat_id: chatId, user_id: userId })
			.modify({ last_read_seq: lastReadSeq });
	}, [userId]);

	return {
		unreadCounts: unreadCounts || {},
		markAsRead
	};
}

// 本地设置 hooks
export function useLocalSetting(key: string) {
	const setting = useLiveQuery(
		async () => {
			const db = getCurrentDatabase();
			if (!db) return null;
			const result = await db.local_settings.get(key);
			return result?.value || null;
		},
		[key]
	);

	const setSetting = useCallback(async (value: string) => {
		await DatabaseService.setSetting(key, value);
	}, [key]);

	return {
		value: setting,
		setValue: setSetting
	};
}
