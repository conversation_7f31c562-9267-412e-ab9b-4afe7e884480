import { auth } from "./utils";

export interface WebSocketMessage {
	type: string;
	data: any;
	timestamp?: number;
}

export interface ChatMessage {
	id: number;
	chat_id: number;
	seq: number;
	sender_id: number;
	type: number;
	content: string;
	content_type: number;
	status: number;
	created_at: string;
	sender_account?: string;
	sender_nickname?: string;
	sender_avatar?: string;
}

export class WebSocketManager {
	private ws: WebSocket | null = null;
	private reconnectAttempts = 0;
	private maxReconnectAttempts = 5;
	private reconnectDelay = 1000;
	private isConnecting = false;
	private messageHandlers: Map<string, ((data: any) => void)[]> = new Map();
	private connectionStateHandlers: ((connected: boolean) => void)[] = [];

	constructor() {
		this.connect();
	}

	// 连接 WebSocket
	private async connect() {
		if (this.isConnecting || this.ws?.readyState === WebSocket.OPEN) {
			return;
		}

		this.isConnecting = true;
		const token = auth.getToken();
		const serverUrl = auth.getServerUrl();

		if (!token || !serverUrl) {
			console.error('WebSocket: Missing token or server URL');
			this.isConnecting = false;
			return;
		}

		try {
			// 将 HTTP URL 转换为 WebSocket URL
			const wsUrl = serverUrl.replace(/^https?:\/\//, 'ws://') + '/api/ws';
			
			console.log('WebSocket: Connecting to', wsUrl);
			this.ws = new WebSocket(wsUrl);

			this.ws.onopen = () => {
				console.log('WebSocket: Connected');
				this.isConnecting = false;
				this.reconnectAttempts = 0;
				
				// 发送认证消息
				this.send({
					type: 'auth',
					data: { token }
				});

				this.notifyConnectionState(true);
			};

			this.ws.onmessage = (event) => {
				try {
					const message: WebSocketMessage = JSON.parse(event.data);
					this.handleMessage(message);
				} catch (error) {
					console.error('WebSocket: Failed to parse message', error);
				}
			};

			this.ws.onclose = (event) => {
				console.log('WebSocket: Disconnected', event.code, event.reason);
				this.isConnecting = false;
				this.ws = null;
				this.notifyConnectionState(false);

				// 自动重连
				if (this.reconnectAttempts < this.maxReconnectAttempts) {
					this.reconnectAttempts++;
					console.log(`WebSocket: Reconnecting in ${this.reconnectDelay}ms (attempt ${this.reconnectAttempts})`);
					setTimeout(() => this.connect(), this.reconnectDelay);
					this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000); // 最大30秒
				}
			};

			this.ws.onerror = (error) => {
				console.error('WebSocket: Error', error);
				this.isConnecting = false;
			};

		} catch (error) {
			console.error('WebSocket: Connection failed', error);
			this.isConnecting = false;
		}
	}

	// 处理收到的消息
	private handleMessage(message: WebSocketMessage) {
		console.log('WebSocket: Received message', message);
		
		const handlers = this.messageHandlers.get(message.type);
		if (handlers) {
			handlers.forEach(handler => {
				try {
					handler(message.data);
				} catch (error) {
					console.error('WebSocket: Message handler error', error);
				}
			});
		}
	}

	// 发送消息
	public send(message: WebSocketMessage) {
		if (this.ws?.readyState === WebSocket.OPEN) {
			message.timestamp = Date.now();
			this.ws.send(JSON.stringify(message));
		} else {
			console.warn('WebSocket: Cannot send message, not connected');
		}
	}

	// 注册消息处理器
	public onMessage(type: string, handler: (data: any) => void) {
		if (!this.messageHandlers.has(type)) {
			this.messageHandlers.set(type, []);
		}
		this.messageHandlers.get(type)!.push(handler);

		// 返回取消注册的函数
		return () => {
			const handlers = this.messageHandlers.get(type);
			if (handlers) {
				const index = handlers.indexOf(handler);
				if (index > -1) {
					handlers.splice(index, 1);
				}
			}
		};
	}

	// 注册连接状态处理器
	public onConnectionState(handler: (connected: boolean) => void) {
		this.connectionStateHandlers.push(handler);
		
		// 立即通知当前状态
		handler(this.isConnected());

		// 返回取消注册的函数
		return () => {
			const index = this.connectionStateHandlers.indexOf(handler);
			if (index > -1) {
				this.connectionStateHandlers.splice(index, 1);
			}
		};
	}

	// 通知连接状态变化
	private notifyConnectionState(connected: boolean) {
		this.connectionStateHandlers.forEach(handler => {
			try {
				handler(connected);
			} catch (error) {
				console.error('WebSocket: Connection state handler error', error);
			}
		});
	}

	// 检查是否已连接
	public isConnected(): boolean {
		return this.ws?.readyState === WebSocket.OPEN;
	}

	// 手动重连
	public reconnect() {
		if (this.ws) {
			this.ws.close();
		}
		this.reconnectAttempts = 0;
		this.reconnectDelay = 1000;
		this.connect();
	}

	// 断开连接
	public disconnect() {
		if (this.ws) {
			this.ws.close();
			this.ws = null;
		}
		this.messageHandlers.clear();
		this.connectionStateHandlers.length = 0;
	}

	// 发送聊天消息
	public sendChatMessage(chatId: number, content: string, contentType: number = 1) {
		this.send({
			type: 'chat_message',
			data: {
				chat_id: chatId,
				content,
				content_type: contentType
			}
		});
	}

	// 加入聊天室
	public joinChat(chatId: number) {
		this.send({
			type: 'join_chat',
			data: { chat_id: chatId }
		});
	}

	// 离开聊天室
	public leaveChat(chatId: number) {
		this.send({
			type: 'leave_chat',
			data: { chat_id: chatId }
		});
	}
}

// 全局 WebSocket 管理器实例
let wsManager: WebSocketManager | null = null;

export function getWebSocketManager(): WebSocketManager {
	if (!wsManager) {
		wsManager = new WebSocketManager();
	}
	return wsManager;
}

export function closeWebSocketManager() {
	if (wsManager) {
		wsManager.disconnect();
		wsManager = null;
	}
}
