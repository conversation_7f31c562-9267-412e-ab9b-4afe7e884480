// See the Electron documentation for details on how to use preload scripts:
// https://www.electronjs.org/docs/latest/tutorial/process-model#preload-scripts

import { contextBridge, ipcRenderer } from 'electron';

// 暴露安全的 API 给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
	// 自启动相关 API
	getAutoLaunchStatus: () => ipcRenderer.invoke('get-auto-launch-status'),
	setAutoLaunch: (enabled: boolean) => ipcRenderer.invoke('set-auto-launch', enabled),
});
