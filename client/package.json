{"name": "liyu", "productName": "liyu", "version": "1.0.0", "private": true, "description": "My Electron application description", "main": ".vite/build/main.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "format": "npx @biomejs/biome format --write", "lint": "npx @biomejs/biome lint --write", "check": "npx @biomejs/biome check --write"}, "keywords": [], "author": {"name": "YonLJ", "email": "<EMAIL>"}, "devDependencies": {"@biomejs/biome": "2.2.2", "@electron-forge/cli": "^7.8.3", "@electron-forge/maker-deb": "^7.8.3", "@electron-forge/maker-rpm": "^7.8.3", "@electron-forge/maker-squirrel": "^7.8.3", "@electron-forge/maker-zip": "^7.8.3", "@electron-forge/plugin-auto-unpack-natives": "^7.8.3", "@electron-forge/plugin-fuses": "^7.8.3", "@electron-forge/plugin-vite": "^7.8.3", "@electron/fuses": "^1.8.0", "@types/electron-squirrel-startup": "^1.0.2", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.2", "electron": "37.4.0", "typescript": "^5.9.2", "vite": "^7.1.4"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.12", "@types/js-cookie": "^3.0.6", "auto-launch": "^5.0.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dexie": "^4.2.0", "dexie-react-hooks": "^4.2.0", "electron-squirrel-startup": "^1.0.1", "jotai": "^2.13.1", "js-cookie": "^3.0.5", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router": "^7.8.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.7"}}