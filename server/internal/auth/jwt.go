package auth

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"

	"liyu/internal/config"
)

// Claims JWT声明
type Claims struct {
	UserID   uint64 `json:"user_id"`
	Account  string `json:"account"`
	DeviceID string `json:"device_id,omitempty"`
	jwt.RegisteredClaims
}

// TokenPair 令牌对
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
}

// Service JWT服务接口
type Service interface {
	GenerateTokenPair(userID uint64, account string) (*TokenPair, error)
	ValidateToken(tokenString string) (*Claims, error)
	RefreshToken(refreshTokenString string) (*TokenPair, error)
}

// JWTService JWT服务实现
type JWTService struct {
	config *config.Config
}

// NewJWTService 创建JWT服务
func NewJWTService(cfg *config.Config) Service {
	return &JWTService{config: cfg}
}

// GenerateTokenPair 生成令牌对
func (s *JWTService) GenerateTokenPair(userID uint64, account string) (*TokenPair, error) {
	// 生成访问令牌
	accessToken, err := s.generateAccessToken(userID, account)
	if err != nil {
		return nil, err
	}

	// 生成刷新令牌
	refreshToken, err := s.generateRefreshToken(userID, account)
	if err != nil {
		return nil, err
	}

	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(s.config.JWT.ExpiresHours * 3600),
	}, nil
}

// generateAccessToken 生成访问令牌
func (s *JWTService) generateAccessToken(userID uint64, account string) (string, error) {
	expirationTime := time.Now().Add(time.Duration(s.config.JWT.ExpiresHours) * time.Hour)

	claims := &Claims{
		UserID:  userID,
		Account: account,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "liyu",
			Subject:   account,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.config.JWT.Secret))
}

// generateRefreshToken 生成刷新令牌
func (s *JWTService) generateRefreshToken(userID uint64, account string) (string, error) {
	expirationTime := time.Now().Add(time.Duration(s.config.JWT.RefreshExpiresHours) * time.Hour)

	claims := &Claims{
		UserID:  userID,
		Account: account,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "liyu",
			Subject:   account,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.config.JWT.Secret + "_refresh"))
}

// ValidateToken 验证访问令牌
func (s *JWTService) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("无效的签名方法")
		}
		return []byte(s.config.JWT.Secret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("无效的令牌")
}

// validateRefreshToken 验证刷新令牌
func (s *JWTService) validateRefreshToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("无效的签名方法")
		}
		return []byte(s.config.JWT.Secret + "_refresh"), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("无效的刷新令牌")
}

// RefreshToken 刷新令牌
func (s *JWTService) RefreshToken(refreshTokenString string) (*TokenPair, error) {
	// 验证刷新令牌
	claims, err := s.validateRefreshToken(refreshTokenString)
	if err != nil {
		return nil, err
	}

	// 生成新的令牌对
	return s.GenerateTokenPair(claims.UserID, claims.Account)
}

// 全局函数（向后兼容）

var globalJWTService Service

// InitJWTService 初始化全局JWT服务
func InitJWTService(cfg *config.Config) {
	globalJWTService = NewJWTService(cfg)
}

// GenerateToken 生成访问令牌（向后兼容）
func GenerateToken(userID uint64, account string) (string, error) {
	if globalJWTService == nil {
		return "", errors.New("JWT service not initialized")
	}
	tokenPair, err := globalJWTService.GenerateTokenPair(userID, account)
	if err != nil {
		return "", err
	}
	return tokenPair.AccessToken, nil
}

// ValidateToken 验证令牌（向后兼容）
func ValidateToken(tokenString string) (*Claims, error) {
	if globalJWTService == nil {
		return nil, errors.New("JWT service not initialized")
	}
	return globalJWTService.ValidateToken(tokenString)
}
