package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"liyu/internal/api/middleware"
	"liyu/internal/common/response"
	"liyu/internal/domain/user"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService user.Service
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userService user.Service) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// ListUsers 获取用户列表
func (h *UserHandler) ListUsers(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	users, total, err := h.userService.ListUsers(c.Request.Context(), page, pageSize)
	if err != nil {
		response.InternalServerError(c, "获取用户列表失败")
		return
	}

	// 转换为响应格式
	userResponses := make([]*user.UserResponse, len(users))
	for i, u := range users {
		userResponses[i] = u.ToResponse()
	}

	response.Pagination(c, userResponses, total, page, pageSize)
}

// GetUser 获取指定用户信息
func (h *UserHandler) GetUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	targetUser, err := h.userService.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		if err == user.ErrUserNotFound {
			response.NotFound(c, "用户不存在")
		} else {
			response.InternalServerError(c, "获取用户信息失败")
		}
		return
	}

	response.Success(c, targetUser.ToResponse())
}

// UpdateUser 更新用户信息
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	// 检查权限：只能更新自己的信息
	currentUserID := middleware.GetUserID(c)
	if currentUserID != userID {
		response.Forbidden(c, "只能更新自己的信息")
		return
	}

	var req user.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "请求参数错误")
		return
	}

	if err := h.userService.UpdateUser(c.Request.Context(), userID, &req); err != nil {
		switch err {
		case user.ErrUserNotFound:
			response.NotFound(c, "用户不存在")
		case user.ErrEmailExists:
			response.Conflict(c, "邮箱已存在")
		case user.ErrPhoneExists:
			response.Conflict(c, "手机号已存在")
		default:
			response.InternalServerError(c, "更新用户信息失败")
		}
		return
	}

	response.Success(c, map[string]interface{}{
		"message": "用户信息更新成功",
	})
}

// DeleteUser 删除用户
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	// 检查权限：只能删除自己
	currentUserID := middleware.GetUserID(c)
	if currentUserID != userID {
		response.Forbidden(c, "只能删除自己的账号")
		return
	}

	// TODO: 实现用户删除逻辑
	response.Success(c, map[string]interface{}{
		"message": "用户删除功能待实现",
	})
}

// UploadAvatar 上传用户头像
func (h *UserHandler) UploadAvatar(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	// 检查权限：只能上传自己的头像
	currentUserID := middleware.GetUserID(c)
	if currentUserID != userID {
		response.Forbidden(c, "只能上传自己的头像")
		return
	}

	// TODO: 实现头像上传逻辑
	response.Success(c, map[string]interface{}{
		"message": "头像上传功能待实现",
	})
}

// ChangePassword 修改用户密码
func (h *UserHandler) ChangePassword(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	// 检查权限：只能修改自己的密码
	currentUserID := middleware.GetUserID(c)
	if currentUserID != userID {
		response.Forbidden(c, "只能修改自己的密码")
		return
	}

	var req struct {
		OldPassword string `json:"old_password" binding:"required"`
		NewPassword string `json:"new_password" binding:"required,min=6"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "请求参数错误")
		return
	}

	if err := h.userService.ChangePassword(c.Request.Context(), userID, req.OldPassword, req.NewPassword); err != nil {
		switch err {
		case user.ErrUserNotFound:
			response.NotFound(c, "用户不存在")
		case user.ErrInvalidPassword:
			response.BadRequest(c, "原密码错误")
		default:
			response.InternalServerError(c, "修改密码失败")
		}
		return
	}

	response.Success(c, map[string]interface{}{
		"message": "密码修改成功",
	})
}

// SearchUsers 搜索用户
func (h *UserHandler) SearchUsers(c *gin.Context) {
	keyword := c.Query("q")
	if keyword == "" {
		response.BadRequest(c, "搜索关键词不能为空")
		return
	}

	// TODO: 实现用户搜索逻辑
	response.Success(c, map[string]interface{}{
		"message": "用户搜索功能待实现",
		"keyword": keyword,
	})
}
