package handlers

import (
	"github.com/gin-gonic/gin"

	"liyu/internal/api/middleware"
	"liyu/internal/auth"
	"liyu/internal/common/response"
	"liyu/internal/domain/user"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	userService user.Service
	jwtService  auth.Service
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(userService user.Service) *AuthHandler {
	return &AuthHandler{
		userService: userService,
	}
}

// LoginRequest 登录请求
type LoginRequest struct {
	Account  string `json:"account" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Account  string  `json:"account" binding:"required,min=3,max=50"`
	Password string  `json:"password" binding:"required,min=6"`
	Nickname *string `json:"nickname" binding:"omitempty,max=50"`
	Email    *string `json:"email" binding:"omitempty,email"`
	Phone    *string `json:"phone" binding:"omitempty,len=11"`
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// AuthResponse 认证响应
type AuthResponse struct {
	User         *user.UserResponse `json:"user"`
	AccessToken  string             `json:"access_token"`
	RefreshToken string             `json:"refresh_token"`
	ExpiresIn    int64              `json:"expires_in"`
}

// Register 用户注册
func (h *AuthHandler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "请求参数错误")
		return
	}

	// 创建用户请求
	createReq := &user.CreateUserRequest{
		Account:  req.Account,
		Password: req.Password,
		Nickname: req.Nickname,
		Email:    req.Email,
		Phone:    req.Phone,
	}

	// 创建用户
	newUser, err := h.userService.CreateUser(c.Request.Context(), createReq)
	if err != nil {
		switch err {
		case user.ErrAccountExists:
			response.Conflict(c, "用户名已存在")
		case user.ErrEmailExists:
			response.Conflict(c, "邮箱已存在")
		case user.ErrPhoneExists:
			response.Conflict(c, "手机号已存在")
		default:
			response.InternalServerError(c, "创建用户失败")
		}
		return
	}

	// 生成JWT令牌
	tokenPair, err := GenerateTokenPair(newUser.ID, newUser.Account)
	if err != nil {
		response.InternalServerError(c, "生成令牌失败")
		return
	}

	// 返回响应
	response.Success(c, AuthResponse{
		User:         newUser.ToResponse(),
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresIn:    tokenPair.ExpiresIn,
	})
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "请求参数错误")
		return
	}

	// 验证用户密码
	authenticatedUser, err := h.userService.VerifyPassword(c.Request.Context(), req.Account, req.Password)
	if err != nil {
		switch err {
		case user.ErrUserNotFound, user.ErrInvalidPassword:
			response.Unauthorized(c, "用户名或密码错误")
		case user.ErrUserDisabled:
			response.Forbidden(c, "账号已被禁用")
		case user.ErrUserDeleted:
			response.Forbidden(c, "账号已被删除")
		default:
			response.InternalServerError(c, "登录失败")
		}
		return
	}

	// 生成JWT令牌
	tokenPair, err := GenerateTokenPair(authenticatedUser.ID, authenticatedUser.Account)
	if err != nil {
		response.InternalServerError(c, "生成令牌失败")
		return
	}

	// 返回响应
	response.Success(c, AuthResponse{
		User:         authenticatedUser.ToResponse(),
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresIn:    tokenPair.ExpiresIn,
	})
}

// RefreshToken 刷新令牌
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "请求参数错误")
		return
	}

	// 刷新令牌
	tokenPair, err := RefreshToken(req.RefreshToken)
	if err != nil {
		response.Unauthorized(c, "无效的刷新令牌")
		return
	}

	// 返回新令牌
	response.Success(c, map[string]interface{}{
		"access_token":  tokenPair.AccessToken,
		"refresh_token": tokenPair.RefreshToken,
		"expires_in":    tokenPair.ExpiresIn,
	})
}

// Logout 用户登出
func (h *AuthHandler) Logout(c *gin.Context) {
	// TODO: 实现令牌黑名单机制
	response.Success(c, map[string]interface{}{
		"message": "登出成功",
	})
}

// GetCurrentUser 获取当前用户信息
func (h *AuthHandler) GetCurrentUser(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "用户未认证")
		return
	}

	currentUser, err := h.userService.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		if err == user.ErrUserNotFound {
			response.NotFound(c, "用户不存在")
		} else {
			response.InternalServerError(c, "获取用户信息失败")
		}
		return
	}

	response.Success(c, currentUser.ToResponse())
}

// 辅助函数

// GenerateTokenPair 生成令牌对（临时实现）
func GenerateTokenPair(userID uint64, account string) (*auth.TokenPair, error) {
	// 这里应该调用全局的JWT服务
	// 临时实现
	accessToken, err := auth.GenerateToken(userID, account)
	if err != nil {
		return nil, err
	}

	return &auth.TokenPair{
		AccessToken:  accessToken,
		RefreshToken: accessToken, // 临时使用相同的令牌
		ExpiresIn:    24 * 3600,   // 24小时
	}, nil
}

// RefreshToken 刷新令牌（临时实现）
func RefreshToken(refreshToken string) (*auth.TokenPair, error) {
	// 验证刷新令牌
	claims, err := auth.ValidateToken(refreshToken)
	if err != nil {
		return nil, err
	}

	// 生成新令牌
	return GenerateTokenPair(claims.UserID, claims.Account)
}
