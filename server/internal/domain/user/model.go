package user

import (
	"time"

	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID        uint64         `gorm:"primaryKey;autoIncrement;comment:用户唯一ID" json:"id"`
	Account   string         `gorm:"type:varchar(50);not null;uniqueIndex:uk_account;comment:用户名，用于登录或显示" json:"account"`
	Password  string         `gorm:"type:varchar(255);not null;comment:加密后的密码" json:"-"`
	Nickname  *string        `gorm:"type:varchar(50);comment:用户昵称" json:"nickname"`
	Avatar    *string        `gorm:"type:varchar(255);comment:头像URL" json:"avatar"`
	Email     *string        `gorm:"type:varchar(100);uniqueIndex:uk_email;comment:邮箱" json:"email"`
	Phone     *string        `gorm:"type:varchar(20);uniqueIndex:uk_phone;comment:手机号" json:"phone"`
	CreatedAt time.Time      `gorm:"type:timestamp(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:timestamp(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`
	Status    Status         `gorm:"type:tinyint;not null;default:1;index:idx_status;comment:账号状态(1:正常,2:禁用,3:删除)" json:"status"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// Status 用户状态枚举
type Status int8

const (
	StatusNormal   Status = 1 // 正常
	StatusDisabled Status = 2 // 禁用
	StatusDeleted  Status = 3 // 删除
)

// String 状态字符串表示
func (s Status) String() string {
	switch s {
	case StatusNormal:
		return "normal"
	case StatusDisabled:
		return "disabled"
	case StatusDeleted:
		return "deleted"
	default:
		return "unknown"
	}
}

// IsValid 检查状态是否有效
func (s Status) IsValid() bool {
	return s >= StatusNormal && s <= StatusDeleted
}

// IsActive 检查用户是否活跃
func (u *User) IsActive() bool {
	return u.Status == StatusNormal
}

// GetDisplayName 获取显示名称
func (u *User) GetDisplayName() string {
	if u.Nickname != nil && *u.Nickname != "" {
		return *u.Nickname
	}
	return u.Account
}

// HasEmail 检查是否有邮箱
func (u *User) HasEmail() bool {
	return u.Email != nil && *u.Email != ""
}

// HasPhone 检查是否有手机号
func (u *User) HasPhone() bool {
	return u.Phone != nil && *u.Phone != ""
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Account  string  `json:"account" binding:"required,min=3,max=50"`
	Password string  `json:"password" binding:"required,min=6"`
	Nickname *string `json:"nickname" binding:"omitempty,max=50"`
	Email    *string `json:"email" binding:"omitempty,email"`
	Phone    *string `json:"phone" binding:"omitempty,len=11"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Nickname *string `json:"nickname" binding:"omitempty,max=50"`
	Avatar   *string `json:"avatar" binding:"omitempty,url"`
	Email    *string `json:"email" binding:"omitempty,email"`
	Phone    *string `json:"phone" binding:"omitempty,len=11"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID       uint64  `json:"id"`
	Account  string  `json:"account"`
	Nickname *string `json:"nickname"`
	Avatar   *string `json:"avatar"`
	Email    *string `json:"email"`
	Phone    *string `json:"phone"`
	Status   Status  `json:"status"`
}

// ToResponse 转换为响应格式
func (u *User) ToResponse() *UserResponse {
	return &UserResponse{
		ID:       u.ID,
		Account:  u.Account,
		Nickname: u.Nickname,
		Avatar:   u.Avatar,
		Email:    u.Email,
		Phone:    u.Phone,
		Status:   u.Status,
	}
}
