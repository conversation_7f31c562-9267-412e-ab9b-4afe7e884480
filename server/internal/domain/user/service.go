package user

import (
	"context"

	"golang.org/x/crypto/bcrypt"
)

// Service 用户服务接口
type Service interface {
	// CreateUser 创建用户
	CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error)
	
	// GetUserByID 根据ID获取用户
	GetUserByID(ctx context.Context, id uint64) (*User, error)
	
	// GetUserByAccount 根据账号获取用户
	GetUserByAccount(ctx context.Context, account string) (*User, error)
	
	// UpdateUser 更新用户信息
	UpdateUser(ctx context.Context, id uint64, req *UpdateUserRequest) error
	
	// VerifyPassword 验证密码
	VerifyPassword(ctx context.Context, account, password string) (*User, error)
	
	// ChangePassword 修改密码
	ChangePassword(ctx context.Context, id uint64, oldPassword, newPassword string) error
	
	// ListUsers 获取用户列表
	ListUsers(ctx context.Context, page, pageSize int) ([]*User, int64, error)
	
	// DisableUser 禁用用户
	DisableUser(ctx context.Context, id uint64) error
	
	// EnableUser 启用用户
	EnableUser(ctx context.Context, id uint64) error
}

// ServiceImpl 用户服务实现
type ServiceImpl struct {
	repo Repository
}

// NewService 创建用户服务
func NewService(repo Repository) Service {
	return &ServiceImpl{repo: repo}
}

// CreateUser 创建用户
func (s *ServiceImpl) CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error) {
	// 检查账号是否已存在
	exists, err := s.repo.ExistsByAccount(ctx, req.Account)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, ErrAccountExists
	}

	// 检查邮箱是否已存在
	if req.Email != nil && *req.Email != "" {
		exists, err := s.repo.ExistsByEmail(ctx, *req.Email)
		if err != nil {
			return nil, err
		}
		if exists {
			return nil, ErrEmailExists
		}
	}

	// 检查手机号是否已存在
	if req.Phone != nil && *req.Phone != "" {
		exists, err := s.repo.ExistsByPhone(ctx, *req.Phone)
		if err != nil {
			return nil, err
		}
		if exists {
			return nil, ErrPhoneExists
		}
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// 创建用户
	user := &User{
		Account:  req.Account,
		Password: string(hashedPassword),
		Nickname: req.Nickname,
		Email:    req.Email,
		Phone:    req.Phone,
		Status:   StatusNormal,
	}

	if err := s.repo.Create(ctx, user); err != nil {
		return nil, err
	}

	return user, nil
}

// GetUserByID 根据ID获取用户
func (s *ServiceImpl) GetUserByID(ctx context.Context, id uint64) (*User, error) {
	return s.repo.GetByID(ctx, id)
}

// GetUserByAccount 根据账号获取用户
func (s *ServiceImpl) GetUserByAccount(ctx context.Context, account string) (*User, error) {
	return s.repo.GetByAccount(ctx, account)
}

// UpdateUser 更新用户信息
func (s *ServiceImpl) UpdateUser(ctx context.Context, id uint64, req *UpdateUserRequest) error {
	// 检查用户是否存在
	user, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	// 检查邮箱是否已被其他用户使用
	if req.Email != nil && *req.Email != "" {
		if user.Email == nil || *user.Email != *req.Email {
			exists, err := s.repo.ExistsByEmail(ctx, *req.Email)
			if err != nil {
				return err
			}
			if exists {
				return ErrEmailExists
			}
		}
	}

	// 检查手机号是否已被其他用户使用
	if req.Phone != nil && *req.Phone != "" {
		if user.Phone == nil || *user.Phone != *req.Phone {
			exists, err := s.repo.ExistsByPhone(ctx, *req.Phone)
			if err != nil {
				return err
			}
			if exists {
				return ErrPhoneExists
			}
		}
	}

	// 准备更新字段
	updates := make(map[string]interface{})
	if req.Nickname != nil {
		updates["nickname"] = *req.Nickname
	}
	if req.Avatar != nil {
		updates["avatar"] = *req.Avatar
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}

	return s.repo.UpdateFields(ctx, id, updates)
}

// VerifyPassword 验证密码
func (s *ServiceImpl) VerifyPassword(ctx context.Context, account, password string) (*User, error) {
	user, err := s.repo.GetByAccount(ctx, account)
	if err != nil {
		return nil, err
	}

	if !user.IsActive() {
		if user.Status == StatusDisabled {
			return nil, ErrUserDisabled
		}
		return nil, ErrUserDeleted
	}

	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return nil, ErrInvalidPassword
	}

	return user, nil
}

// ChangePassword 修改密码
func (s *ServiceImpl) ChangePassword(ctx context.Context, id uint64, oldPassword, newPassword string) error {
	user, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(oldPassword)); err != nil {
		return ErrInvalidPassword
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 更新密码
	return s.repo.UpdateFields(ctx, id, map[string]interface{}{
		"password": string(hashedPassword),
	})
}

// ListUsers 获取用户列表
func (s *ServiceImpl) ListUsers(ctx context.Context, page, pageSize int) ([]*User, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	return s.repo.List(ctx, offset, pageSize)
}

// DisableUser 禁用用户
func (s *ServiceImpl) DisableUser(ctx context.Context, id uint64) error {
	return s.repo.UpdateFields(ctx, id, map[string]interface{}{
		"status": StatusDisabled,
	})
}

// EnableUser 启用用户
func (s *ServiceImpl) EnableUser(ctx context.Context, id uint64) error {
	return s.repo.UpdateFields(ctx, id, map[string]interface{}{
		"status": StatusNormal,
	})
}
