package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	_ "github.com/go-sql-driver/mysql"

	"liyu/internal/api/handlers"
	"liyu/internal/api/routes"
	"liyu/internal/auth"
	"liyu/internal/config"
	"liyu/internal/database"
	"liyu/internal/domain/user"
	"liyu/internal/websocket"
)

const VERSION = "1.0.1"

var (
	configPath = "config.yaml"
)

func main() {
	log.Printf("启动 Liyu 即时通讯服务器 v%s", VERSION)

	// 1. 读取配置文件
	log.Println("正在加载配置文件...")
	if err := config.Load(configPath); err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}
	log.Println("配置文件加载成功")

	// 2. 检查目标数据库是否存在
	dbName := config.AppConfig.Database.MySQL.Database
	log.Printf("正在检查数据库 '%s' 是否存在...", dbName)
	isInstalled, err := checkDatabaseExists(dbName)
	if err != nil {
		log.Fatalf("检查数据库失败: %v", err)
	}

	if !isInstalled {
		// 3. 如果数据库不存在，执行安装过程
		log.Printf("数据库 '%s' 不存在，开始安装应用...", dbName)
		if err := installApplication(dbName); err != nil {
			log.Fatalf("应用安装失败: %v", err)
		}
		log.Println("应用安装完成")
	}

	// 4. 连接到目标数据库
	log.Println("正在连接数据库...")
	if err := database.Init(config.AppConfig); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	defer database.Close()
	log.Println("数据库连接成功")

	if isInstalled {
		// 5. 如果数据库存在，检查版本并升级
		log.Printf("数据库 '%s' 已存在，检查版本...", dbName)
		if err := checkAndUpgradeDatabase(); err != nil {
			// 如果版本表不存在，说明是旧版本数据库，需要重新初始化
			if strings.Contains(err.Error(), "版本表不存在") {
				log.Println("检测到旧版本数据库，需要重新初始化...")
				log.Fatalf("请备份数据后删除数据库重新安装，或手动创建版本表")
			}
			log.Fatalf("数据库升级失败: %v", err)
		}
	}

	// 6. 启动服务器
	log.Println("正在启动服务器...")
	startServer()
}

// checkDatabaseExists 检查数据库是否存在
func checkDatabaseExists(dbName string) (bool, error) {
	cfg := config.AppConfig.Database.MySQL

	// 连接到MySQL服务器（不指定数据库）
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/",
		cfg.Username, cfg.Password, cfg.Host, cfg.Port)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return false, err
	}
	defer db.Close()

	// 检查数据库是否存在
	var count int
	query := "SELECT COUNT(*) FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?"
	err = db.QueryRow(query, dbName).Scan(&count)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// installApplication 安装应用
func installApplication(dbName string) error {
	cfg := config.AppConfig.Database.MySQL

	// 连接到MySQL服务器
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/",
		cfg.Username, cfg.Password, cfg.Host, cfg.Port)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return err
	}
	defer db.Close()

	// 创建数据库
	log.Printf("正在创建数据库 '%s'...", dbName)
	_, err = db.Exec(fmt.Sprintf("CREATE DATABASE `%s` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", dbName))
	if err != nil {
		return fmt.Errorf("创建数据库失败: %w", err)
	}

	// 切换到新创建的数据库
	_, err = db.Exec(fmt.Sprintf("USE `%s`", dbName))
	if err != nil {
		return fmt.Errorf("切换数据库失败: %w", err)
	}

	// 执行初始化SQL
	log.Println("正在执行初始化SQL...")
	if err := executeInitSQL(db); err != nil {
		return fmt.Errorf("执行初始化SQL失败: %w", err)
	}

	// 插入版本记录
	log.Printf("正在记录应用版本 %s...", VERSION)
	if err := insertVersionRecord(db, VERSION); err != nil {
		return fmt.Errorf("记录版本失败: %w", err)
	}

	return nil
}

// executeInitSQL 执行初始化SQL
func executeInitSQL(db *sql.DB) error {
	initSQLPath := "./migrations/init.sql"

	// 读取init.sql文件
	sqlContent, err := os.ReadFile(initSQLPath)
	if err != nil {
		return fmt.Errorf("读取init.sql文件失败: %w", err)
	}

	// 分割SQL语句并逐条执行
	sqlStatements := splitSQLStatements(string(sqlContent))
	log.Printf("共分割出 %d 条SQL语句", len(sqlStatements))

	for i, stmt := range sqlStatements {
		stmt = strings.TrimSpace(stmt)
		log.Printf("处理SQL语句 %d: 长度=%d, 前50字符=%s", i+1, len(stmt), stmt[:min(50, len(stmt))])

		if stmt == "" {
			log.Printf("跳过SQL语句 %d (空)", i+1)
			continue
		}

		// 检查是否包含实际的SQL语句（不只是注释）
		lines := strings.Split(stmt, "\n")
		hasSQL := false
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line != "" && !strings.HasPrefix(line, "--") {
				hasSQL = true
				break
			}
		}

		if !hasSQL {
			log.Printf("跳过SQL语句 %d (只有注释)", i+1)
			continue
		}

		log.Printf("执行SQL语句 %d/%d", i+1, len(sqlStatements))
		_, err = db.Exec(stmt)
		if err != nil {
			return fmt.Errorf("执行SQL语句失败 (第%d条): %w\nSQL: %s", i+1, err, stmt)
		}
		log.Printf("SQL语句 %d 执行成功", i+1)
	}

	return nil
}

// splitSQLStatements 分割SQL语句
func splitSQLStatements(sqlContent string) []string {
	// 按分号分割SQL语句
	statements := strings.Split(sqlContent, ";")
	var result []string

	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		// 跳过空语句和纯注释行
		if stmt == "" {
			continue
		}

		// 跳过纯注释行
		lines := strings.Split(stmt, "\n")
		hasContent := false
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line != "" && !strings.HasPrefix(line, "--") {
				hasContent = true
				break
			}
		}

		if hasContent {
			result = append(result, stmt)
		}
	}

	return result
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// isValidVersion 检查是否是有效的版本号格式（x.y.z）
func isValidVersion(version string) bool {
	matched, _ := regexp.MatchString(`^\d+\.\d+\.\d+$`, version)
	return matched
}

// parseVersion 解析版本号为数字数组
func parseVersion(version string) ([]int, error) {
	parts := strings.Split(version, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("无效的版本格式: %s", version)
	}

	var nums []int
	for _, part := range parts {
		num, err := strconv.Atoi(part)
		if err != nil {
			return nil, fmt.Errorf("无效的版本号: %s", version)
		}
		nums = append(nums, num)
	}
	return nums, nil
}

// compareVersions 比较两个版本号
// 返回值：-1表示v1<v2，0表示v1=v2，1表示v1>v2
func compareVersions(v1, v2 string) int {
	nums1, err1 := parseVersion(v1)
	nums2, err2 := parseVersion(v2)

	if err1 != nil || err2 != nil {
		// 如果解析失败，按字符串比较
		if v1 < v2 {
			return -1
		} else if v1 > v2 {
			return 1
		}
		return 0
	}

	for i := 0; i < 3; i++ {
		if nums1[i] < nums2[i] {
			return -1
		} else if nums1[i] > nums2[i] {
			return 1
		}
	}
	return 0
}

// sortVersions 对版本号数组进行排序
func sortVersions(versions []string) {
	sort.Slice(versions, func(i, j int) bool {
		return compareVersions(versions[i], versions[j]) < 0
	})
}

// insertVersionRecord 插入版本记录
func insertVersionRecord(db *sql.DB, version string) error {
	query := `INSERT INTO app_versions (version, applied_at) VALUES (?, NOW())`
	_, err := db.Exec(query, version)
	return err
}

// checkAndUpgradeDatabase 检查并升级数据库
func checkAndUpgradeDatabase() error {
	db := database.GetDB()

	// 获取底层的sql.DB
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}

	// 获取当前数据库版本
	currentDBVersion, err := getCurrentDatabaseVersion(sqlDB)
	if err != nil {
		return fmt.Errorf("获取数据库版本失败: %w", err)
	}

	log.Printf("当前数据库版本: %s, 应用版本: %s", currentDBVersion, VERSION)

	// 比较版本
	comparison := compareVersions(currentDBVersion, VERSION)

	if comparison == 0 {
		log.Println("数据库版本已是最新，无需升级")
		return nil
	} else if comparison > 0 {
		return fmt.Errorf("数据库版本 %s 高于应用版本 %s，不支持降级，请检查应用版本", currentDBVersion, VERSION)
	}

	// 执行版本升级
	log.Printf("开始从版本 %s 升级到 %s...", currentDBVersion, VERSION)
	if err := upgradeDatabase(currentDBVersion, VERSION); err != nil {
		return fmt.Errorf("数据库升级失败: %w", err)
	}

	log.Println("数据库升级完成")
	return nil
}

// getCurrentDatabaseVersion 获取当前数据库版本
func getCurrentDatabaseVersion(db *sql.DB) (string, error) {
	// 首先检查版本表是否存在
	var tableExists int
	checkTableQuery := `SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'app_versions'`
	err := db.QueryRow(checkTableQuery).Scan(&tableExists)
	if err != nil {
		return "", err
	}

	if tableExists == 0 {
		// 版本表不存在，说明数据库未初始化，应该执行完整安装
		return "", fmt.Errorf("数据库未初始化，版本表不存在")
	}

	var version string
	query := `SELECT version FROM app_versions ORDER BY applied_at DESC LIMIT 1`
	err = db.QueryRow(query).Scan(&version)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", fmt.Errorf("版本表存在但无版本记录，数据库状态异常")
		}
		return "", err
	}
	return version, nil
}

// upgradeDatabase 升级数据库
func upgradeDatabase(fromVersion, toVersion string) error {
	gormDB := database.GetDB()
	sqlDB, err := gormDB.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}

	// 获取需要执行的升级SQL文件列表
	upgradeFiles, err := getUpgradeFiles(fromVersion, toVersion)
	if err != nil {
		return err
	}

	if len(upgradeFiles) == 0 {
		log.Println("没有需要执行的升级文件")
		// 即使没有升级文件，也要更新版本记录
		if err := insertVersionRecord(sqlDB, toVersion); err != nil {
			return fmt.Errorf("更新版本记录失败: %w", err)
		}
		return nil
	}

	// 依次执行升级文件
	for _, file := range upgradeFiles {
		log.Printf("正在执行升级文件: %s", file)
		if err := executeUpgradeSQL(sqlDB, file); err != nil {
			return fmt.Errorf("执行升级文件 %s 失败: %w", file, err)
		}
	}

	// 更新版本记录
	if err := insertVersionRecord(sqlDB, toVersion); err != nil {
		return fmt.Errorf("更新版本记录失败: %w", err)
	}

	return nil
}

// getUpgradeFiles 获取需要执行的升级文件列表
// 规则：执行大于等于fromVersion且小于toVersion的所有版本SQL文件
func getUpgradeFiles(fromVersion, toVersion string) ([]string, error) {
	// 扫描migrations目录，获取所有版本SQL文件
	files, err := os.ReadDir("./migrations")
	if err != nil {
		return nil, fmt.Errorf("读取migrations目录失败: %w", err)
	}

	var versionFiles []string
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		name := file.Name()
		// 检查是否是版本SQL文件（格式：x.y.z.sql）
		if strings.HasSuffix(name, ".sql") && name != "init.sql" {
			version := strings.TrimSuffix(name, ".sql")
			if isValidVersion(version) {
				versionFiles = append(versionFiles, version)
			}
		}
	}

	// 排序版本文件
	sortVersions(versionFiles)

	// 筛选需要执行的文件
	var upgradeFiles []string
	for _, version := range versionFiles {
		// 大于等于fromVersion且小于toVersion
		if compareVersions(version, fromVersion) >= 0 && compareVersions(version, toVersion) < 0 {
			upgradeFiles = append(upgradeFiles, version+".sql")
		}
	}

	return upgradeFiles, nil
}

// executeUpgradeSQL 执行升级SQL文件
func executeUpgradeSQL(db *sql.DB, filename string) error {
	sqlPath := fmt.Sprintf("./migrations/%s", filename)

	// 读取SQL文件
	sqlContent, err := os.ReadFile(sqlPath)
	if err != nil {
		return fmt.Errorf("读取SQL文件失败: %w", err)
	}

	// 分割SQL语句并逐条执行
	sqlStatements := splitSQLStatements(string(sqlContent))
	log.Printf("升级文件 %s 共分割出 %d 条SQL语句", filename, len(sqlStatements))

	for i, stmt := range sqlStatements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" {
			continue
		}

		// 检查是否包含实际的SQL语句（不只是注释）
		lines := strings.Split(stmt, "\n")
		hasSQL := false
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line != "" && !strings.HasPrefix(line, "--") {
				hasSQL = true
				break
			}
		}

		if !hasSQL {
			continue
		}

		log.Printf("执行升级SQL语句 %d/%d", i+1, len(sqlStatements))
		_, err = db.Exec(stmt)
		if err != nil {
			return fmt.Errorf("执行SQL失败: %w", err)
		}
	}

	return nil
}

// startServer 启动服务器
func startServer() {
	// 初始化JWT服务
	auth.InitJWTService(config.AppConfig)

	// 设置Gin模式
	if !config.AppConfig.Development.Enabled {
		gin.SetMode(gin.ReleaseMode)
	}

	// 初始化服务
	userRepo := user.NewGormRepository(database.GetDB())
	userService := user.NewService(userRepo)

	// 初始化WebSocket服务
	wsService := websocket.NewWebSocketService(database.GetDB())

	// 初始化处理器
	authHandler := handlers.NewAuthHandler(userService)
	userHandler := handlers.NewUserHandler(userService)
	chatHandler := handlers.NewChatHandler()
	uploadHandler := handlers.NewUploadHandler()
	systemHandler := handlers.NewSystemHandler()

	// 获取WebSocket管理器
	wsManager := wsService.GetManager()

	// 创建服务器
	httpServer := createHTTPServer(
		authHandler,
		userHandler,
		chatHandler,
		uploadHandler,
		systemHandler,
		wsManager,
	)

	wsServer := createWebSocketServer(wsService)

	// 启动服务器
	go func() {
		log.Printf("HTTP服务器启动在 %s", config.AppConfig.GetHTTPAddr())
		if config.AppConfig.Server.HTTP.EnableSSL {
			if err := httpServer.ListenAndServeTLS(
				config.AppConfig.Server.HTTP.SSL.CertFile,
				config.AppConfig.Server.HTTP.SSL.KeyFile,
			); err != nil && err != http.ErrServerClosed {
				log.Fatalf("HTTP服务器启动失败: %v", err)
			}
		} else {
			if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				log.Fatalf("HTTP服务器启动失败: %v", err)
			}
		}
	}()

	go func() {
		log.Printf("WebSocket服务器启动在 %s", config.AppConfig.GetWebSocketAddr())
		if config.AppConfig.Server.WebSocket.EnableSSL {
			if err := wsServer.ListenAndServeTLS(
				config.AppConfig.Server.WebSocket.SSL.CertFile,
				config.AppConfig.Server.WebSocket.SSL.KeyFile,
			); err != nil && err != http.ErrServerClosed {
				log.Fatalf("WebSocket服务器启动失败: %v", err)
			}
		} else {
			if err := wsServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				log.Fatalf("WebSocket服务器启动失败: %v", err)
			}
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("正在关闭服务器...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭WebSocket服务
	wsService.Stop()

	// 关闭HTTP服务器
	if err := httpServer.Shutdown(ctx); err != nil {
		log.Printf("HTTP服务器关闭失败: %v", err)
	}

	// 关闭WebSocket服务器
	if err := wsServer.Shutdown(ctx); err != nil {
		log.Printf("WebSocket服务器关闭失败: %v", err)
	}

	log.Println("服务器已关闭")
}

// createHTTPServer 创建HTTP服务器
func createHTTPServer(
	authHandler *handlers.AuthHandler,
	userHandler *handlers.UserHandler,
	chatHandler *handlers.ChatHandler,
	uploadHandler *handlers.UploadHandler,
	systemHandler *handlers.SystemHandler,
	wsManager *websocket.Manager,
) *http.Server {
	router := gin.New()

	// 设置路由
	routes.SetupRoutes(
		router,
		authHandler,
		userHandler,
		chatHandler,
		uploadHandler,
		systemHandler,
		wsManager,
	)

	return &http.Server{
		Addr:    config.AppConfig.GetHTTPAddr(),
		Handler: router,
	}
}

// createWebSocketServer 创建WebSocket服务器
func createWebSocketServer(wsService *websocket.WebSocketService) *http.Server {
	router := gin.New()

	// 设置WebSocket路由
	routes.SetupWebSocketRoutesWithService(router, wsService)

	return &http.Server{
		Addr:    config.AppConfig.GetWebSocketAddr(),
		Handler: router,
	}
}
