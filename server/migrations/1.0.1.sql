-- 版本升级脚本: 1.0.1 -> 1.1.0
-- 升级内容: 添加聊天设置表

-- 创建聊天设置表
CREATE TABLE IF NOT EXISTS `chat_settings` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `chat_id` BIGINT UNSIGNED NOT NULL COMMENT '会话ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `muted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否免打扰',
  `pinned` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否置顶',
  `created_at` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_chat_user_settings` (`chat_id`, `user_id`),
  <PERSON><PERSON>Y `idx_user_settings` (`user_id`),
  CONSTRAINT `fk_chat_settings_chat` FOREIGN KEY (`chat_id`) REFERENCES `chats` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_chat_settings_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天设置表';
